{"name": "fvh-si", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "pro": "vite --mode production", "build": "vite build", "build-dev": "vite build --mode development", "preview": "vite preview", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc"}, "dependencies": {"@azure/msal-browser": "^4.10.0", "@azure/msal-react": "^3.0.9", "@cyntler/react-doc-viewer": "^1.16.3", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@react-pdf-viewer/core": "^3.12.0", "@redux-devtools/extension": "^3.2.5", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-devtools": "^5.72.2", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.10.9", "antd": "^5.24.6", "antd-img-crop": "^4.22.0", "axios": "^1.3.4", "axios-retry": "^4.5.0", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "exceljs": "^4.4.0", "fast-xml-parser": "^5.2.0", "heic2any": "^0.0.4", "js-file-download": "^0.4.12", "localforage": "^1.10.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "match-sorter": "^8.0.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "4.8.69", "query-string": "^9.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-resizable": "^3.0.5", "react-router-dom": "^7.5.0", "react-timer-hook": "^4.0.5", "sort-by": "^1.2.0", "use-deep-compare-effect": "^1.8.1", "use-query-params": "^2.2.1", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@eslint/js": "^9.24.0", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/vite": "^4.1.3", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react-swc": "^3.0.0", "autoprefixer": "^10.4.19", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "postcss": "^8.4.38", "prettier": "^3.5.3", "sass": "^1.58.3", "tailwindcss": "^4.1.3", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-plugin-pwa": "^1.0.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-window": "^7.3.0"}}