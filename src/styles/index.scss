@use './variables';

body {
  overflow: hidden;
}

pre {
  white-space: pre-wrap; /* <PERSON> phép xuống dòng tự động */
  word-wrap: break-word; /* <PERSON><PERSON>t từ nếu quá dài */
  font-family: inherit; /* Dùng font giống thẻ div */
  font-size: inherit; /* (tu<PERSON> chọn) đồng bộ kích cỡ chữ với div */
  margin: 0; /* (tu<PERSON> chọn) bỏ margin mặc định của pre */
}

button:not(.ant-btn-sm) i.fa-solid,
i.fa-regular,
i.fas,
i.fa {
  padding-right: 5px;
}

button.ant-btn.ant-btn-color-cyan {
  box-shadow: none;
}

.ant-layout {
  background: white;
}

.ant-input-search .ant-input-search-button {
  height: 31px;
}

.text-primary {
  cursor: pointer;
  color: variables.$primaryTextColor !important;
}

a {
  color: variables.$primaryTextColor;
  text-decoration: none;
}

.ant-layout-sider-children {
  background-color: #f3fafc;
}

// .ant-menu-root.ant-menu-inline.ant-menu-light {
//   border: none;
// }

$varSpacer: 1rem;
$varSubHeader: 65px;
.sub-header {
  margin: calc($varSpacer * 0.5);
}

.sticky-top {
  position: sticky;
  top: 0;
  background: white;
  z-index: 3;
}

.sticky-bottom {
  position: sticky;
  top: 0;
  background: white;
  z-index: 3;
}

$varHeaderHeight: 44px;
.header {
  min-height: $varHeaderHeight;
  background: linear-gradient(to right, #66c3eb, #89d6b5);
  border-radius: 0 0 13px 13px;
  display: flex;
  align-items: center;
  justify-content: center;

  .header-back {
    background: transparent;
    border-radius: 999px;
    border: 2px solid white;
    color: white;
    position: absolute;
    right: 19px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .header-title {
    font-weight: bold;
    font-size: 1.2em;
    color: white;
    text-align: center;
    padding: 0 50px 0 50px;
  }
}

.logo {
  background: white;
  margin: 5px;
  box-shadow: 0 0 5px 0px lightgrey;
  height: 80px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;

  .logo-spvb {
    padding: 10px;
    width: 141px;
  }
  .Logo {
    padding: 10px;
    width: 95px;
    margin: 0 0px;
  }
}

.sidebar {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 90px);

  .sidebar-menu {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: scroll;

    .sidebar-menu-item {
      display: flex;
      align-items: center;
      background: white;
      margin: 5px;
      padding: 10px 15px;
      border-radius: 7px;
      height: 50px;
      font-weight: bold;

      img {
        width: 30px;
        margin-right: 10px;
      }
    }

    .active {
      color: variables.$primaryColor;
      background: #cce7f6;
    }
  }

  .sidebar-account {
    height: auto;
    padding: 5px;
    background: red;
    text-align: center;
  }
}

.overlay {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.158);
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.custom-table .ant-table-thead > tr > th {
  background-color: #73a9ad !important;
  color: white !important;
  //  white-space: nowrap; /* Prevent header text from wrapping */
  text-overflow: ellipsis; /* Add ellipsis for long text */
  overflow: hidden; /* Hide overflow text */
  height: 10px; /* Set a fixed height for the header */
  line-height: 1em; /* Center the text vertically */
  padding: 5px !important;
  // text-align: left;
}

.custom-table {
  .ant-dropdown-trigger.ant-table-filter-trigger {
    padding-right: 0.2rem;
    padding-left: 0;
    margin-left: 0;

    &.active {
      background-color: yellow;
    }
  }

  .ant-table-cell.ant-table-cell-ellipsis.ant-table-column-has-sorters {
    padding: 0.1rem !important;
  }
}

// .custom-table .ant-table-tbody > tr > td.number{
//   text-align: right !important;
// }

// .custom-table .ant-table-summary > tr > td.number {
//   text-align: right !important;
// }

// .ant-table-fixed {
//    table-layout: fixed;
//   }

//   .ant-table-tbody > tr > td {
//     word-wrap: break-word;
//   //  word-break: break-all;
//   }

.custom-modal {
  top: 5px;

  .ant-modal-title {
    position: absolute;
    background: linear-gradient(to right, #66c3eb, #89d6b5);
    top: 0;
    left: 0;
    width: 100%;
    height: 44px;
    border-radius: 0 0 11px 11px;
    text-align: left;
    padding: 17px;
    color: black;
    font-weight: bold;
    padding-right: 52px;
    display: flex;
    align-items: center;
    justify-content: center; // Center the content horizontally
    word-break: break-word;
  }

  .ant-modal-body {
    margin-top: 44px;
  }

  .ant-modal-close {
    color: black;
    border: 2px solid black;
    border-radius: 264px;
    width: 30px;
    top: 7px;
    height: 30px;

    &:hover {
      color: black !important;
    }
  }

  hr.vertical {
    width: 4px;
    border-radius: 20px;
    height: 55%;
    background-color: #5bc2dc;

    /* or height in PX */
  }

  .horizontal-line-shape {
    border-top: 10px solid #5bc2dc;
    width: 100%;
    margin-top: calc($varSpacer * 0.5);
  }

  // .btn-form-modal {
  //   margin-right: calc($varSpacer * 0.75);
  //   width: 100px;
  // }
}

.custom-modal-search {
  .ant-modal-content {
    width: 100%;
  }
  .ant-modal-title {
    position: absolute;
    background: #5bc2dc;
    top: 0;
    left: 0;
    width: 100%;
    height: 57px;
    border-radius: 0 0 11px 11px;
    text-align: left;
    padding: 17px;
    color: white;
    font-weight: bold;
    padding-right: 52px;
    display: flex;
    align-items: center;
    word-break: break-word;
  }
  .ant-modal-close {
    color: white;
    border: 2px solid white;
    border-radius: 264px;
    width: 30px;
    top: 10px;
    height: 30px;

    &:hover {
      color: white !important;
    }
  }

  .horizontal-line-shape {
    border-top: 10px solid #5bc2dc;
    width: 100%;
    margin-top: calc($varSpacer * 0.5);
  }

  .btn-search-popup {
    margin-right: calc($varSpacer * 0.75);
    width: 100px;
  }
}

.btn-wrap {
  height: auto;
  white-space: normal;
  // word-break: break-word;
  padding: 5px;
}

.modal-full {
  width: 100vw;
  transform-origin: 1052px 351px;
  margin: 0;
  padding: 0;
  max-width: 100vw;
  height: 100vh;
  top: 0;

  .ant-modal-close {
    display: none;
  }

  .ant-modal-content {
    width: 100vw;
    height: 100vh;
    overflow: auto;
    border-radius: 0;
    padding: 0;
  }
}

// sidebar btn
.ant-layout .ant-layout-sider-light .ant-layout-sider-zero-width-trigger {
  z-index: 999;
  top: 46px;
  box-shadow: 0 0 5px lightgray;
}

.modal-filter-popup {
  top: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  height: 100%;
  margin-right: 0;
  margin-left: auto;

  .ant-modal-content {
    width: auto;
    max-width: none;
    height: 100%;
  }
  .ant-modal-body {
    height: 70%;
  }

  .ant-modal-close {
    color: white;
    border: 2px solid white;
    border-radius: 264px;
    width: 30px;
    top: 10px;
    height: 30px;

    &:hover {
      color: white !important;
    }
  }
}

.ant-input-search .anticon-search {
  vertical-align: middle;
}

.ant-checkbox-group {
  .ant-checkbox-wrapper {
    margin: 4px 0px;
  }
}

hr.vertical {
  width: 4px;
  border-radius: 20px;
  height: 55%;
  background-color: #5bc2dc;

  /* or height in PX */
}

.ant-table-wrapper .ant-table-row-expand-icon-cell .ant-table-row-expand-icon {
  display: none !important;
}
.ant-table-wrapper .ant-table-row-indent + .ant-table-row-expand-icon {
  margin-inline-end: 0px !important;
}

[class*='disabled'],
input[disabled],
.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector,
.ant-select-disabled.ant-select-multiple .ant-select-selection-item {
  color: black !important;
  background-color: #00000003 !important;
}

//menu
.ant-menu-root {
  border: none;
  background-color: transparent;
}

.ant-menu-item {
  padding: 5px;
  min-height: 40px;
  height: auto !important;
}

.ant-menu-submenu-title {
  font-size: 13px;
  padding: 5px;
  min-height: 40px;
  height: auto !important;
}

.ant-menu-item .ant-menu-submenu-title {
  border-radius: 7px;
  font-weight: bold;
  margin: 5px;
  padding: 10px 15px;
}

.ant-menu-item-selected {
  background-color: variables.$primaryColor !important;
  color: white !important;
}
// end menu

// sub menu
.ant-menu-title-content {
  font-size: 13px;
  height: auto !important;
  padding: 10px 5px;
  font-weight: bold;
  line-height: 15px;
  line-break: auto;
  white-space: normal; /* Cho phép xuống dòng */
  word-wrap: break-word;
}

.ant-menu-submenu-inline {
  display: flex;
  flex-direction: column;
}

.ant-menu-sub .ant-menu-inline {
  height: auto !important;
}
//end sub menu

.small-menu {
  //menu
  .ant-menu-item {
    padding: 0px;
    min-height: 30px;
  }

  .ant-menu-submenu-title {
    padding: 0px;
    min-height: 30px;
    padding-right: 33px !important;
  }

  .ant-menu-item .ant-menu-submenu-title {
    font-weight: bold;
    margin: 2px;
    padding: 5px 7px;
  }
  // end menu

  // sub menu
  .ant-menu-title-content {
    padding: 5px 0px;
    font-weight: normal;
    line-height: 15px;
  }

  .ant-menu-submenu-title {
    padding-right: 33px !important;
    .ant-menu-title-content {
      font-weight: bold;
    }
  }

  .ant-menu-submenu-selected {
    .ant-menu-submenu-title {
      .ant-menu-title-content {
        color: black;
      }
    }
  }
  //end sub menu
}

.divider.primary {
  height: 5px;
  background: variables.$primaryColor;
  border: none;
  border-radius: 999px;
}

.custom-checkbox-role-indicator {
  .ant-checkbox-group {
    align-items: center;
  }
  .ant-checkbox-wrapper {
    align-items: normal !important;
    // flex-wrap: wrap !important;
    margin: 4px 10px;
  }
  .checkbox-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.custom-icon-upload {
  .ant-upload-list-item-container {
    width: 40px !important; /* Adjust width */
    height: 40px !important; /* Adjust height */
    max-width: 40px !important;
    max-height: 40px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    overflow: hidden !important;
  }
  /* Remove border and shadow from each item in the upload list */
  .ant-upload-list-item {
    border: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
  }
}

.highlight-row,
.highlight-row > td {
  background-color: #d5fdd5 !important;
  font-weight: 700;
}

.highlight-row-gray,
.highlight-row-gray > td {
  background-color: lightgray !important;
  font-weight: 700;
}

.highlight-row-red,
.highlight-row-red > td {
  background-color: rgb(227, 177, 177) !important;
}

.highlight-row-blue,
.highlight-row-blue > td {
  color: blue !important;
}

.hover-row:hover,
.hover-row:hover > td {
  background-color: #d5fdd5 !important;
}
.react-resizable {
  position: relative;
  background-clip: padding-box;
}

// Overlay to prevent interactions while resizing
.resize-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 9998; // Just below the guide
  cursor: col-resize;
  user-select: none;
  -webkit-user-select: none;
}

.resize-guide {
  position: fixed;
  top: 0;
  width: 2px;
  height: 100vh;
  background-color: variables.$primaryColor;
  pointer-events: none;
  z-index: 9999;
  transition: opacity 0.2s;
}

.react-resizable-handle {
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  z-index: 1;
}

// Prevent text selection during resize
// .custom-table {
//   .ant-table-thead {
//     th {
//       user-select: none;
//       position: relative;
//     }
//   }
// }

// Prevent text selection globally during resize
body.resizing {
  user-select: none;
  -webkit-user-select: none;
}


.checkbox-red .ant-checkbox-inner,
.checkbox-red:hover .ant-checkbox-inner {
  border-color: red !important;
  background-color: red !important;
}

.checkbox-red.ant-checkbox-checked .ant-checkbox-inner,
.checkbox-red.ant-checkbox-checked:hover .ant-checkbox-inner {
  border-color: red !important;
  background-color: red !important;
}


.checkbox-warning .ant-checkbox-inner,
.checkbox-warning:hover .ant-checkbox-inner {
  border-color: #faad14 !important;
  background-color: #faad14 !important;
}

.checkbox-warning.ant-checkbox-checked .ant-checkbox-inner,
.checkbox-warning.ant-checkbox-checked:hover .ant-checkbox-inner {
  border-color: #faad14 !important;
  background-color: #faad14 !important;
}
