import { Workbook } from 'exceljs'
import config from '../../common/config'
import { setCellValueByName } from '../../SI/helper'

const numberToWordsVietnamese = (num) => {
  const ones = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín']
  const tens = [
    '',
    'mười',
    'hai mươ<PERSON>',
    'ba mươ<PERSON>',
    'bốn mươi',
    'năm mươi',
    's<PERSON><PERSON> mươ<PERSON>',
    'b<PERSON><PERSON> mươ<PERSON>',
    'tám mươi',
    'ch<PERSON> mư<PERSON>',
  ]

  const toVietnameseWords = (n) => {
    if (n < 10) return ones[n]

    if (n < 100) {
      const chuc = Math.floor(n / 10)
      const donVi = n % 10
      if (chuc === 0) return 'lẻ ' + ones[donVi]
      return tens[chuc] + (donVi ? ' ' + ones[donVi] : '')
    }

    if (n < 1000) {
      const tram = Math.floor(n / 100)
      const du = n % 100
      return ones[tram] + ' trăm' + (du ? ' ' + toVietnameseWords(du) : '')
    }

    if (n < 1000000) {
      const nghin = Math.floor(n / 1000)
      const du = n % 1000
      return toVietnameseWords(nghin) + ' ngàn' + (du ? ' ' + toVietnameseWords(du) : '')
    }

    if (n < **********) {
      const trieu = Math.floor(n / 1000000)
      const du = n % 1000000
      return toVietnameseWords(trieu) + ' triệu' + (du ? ' ' + toVietnameseWords(du) : '')
    }

    const ty = Math.floor(n / **********)
    const du = n % **********
    return toVietnameseWords(ty) + ' tỷ' + (du ? ' ' + toVietnameseWords(du) : '')
  }

  const raw = num === 0 ? 'Không đồng.' : toVietnameseWords(num).replace(/\s+/g, ' ').trim()

  const result = raw.charAt(0).toUpperCase() + raw.slice(1)
  return num === 0 ? 'Không đồng.' : result + ' đồng chẵn.'
}

// Helper functions for data processing
const getVisitDates = (selectedPatientVisitMappingViews) => {
  const sortedVisits =
    selectedPatientVisitMappingViews && selectedPatientVisitMappingViews.length > 0
      ? [...selectedPatientVisitMappingViews].sort(
          (a, b) => new Date(a.actual_visit_datetime) - new Date(b.actual_visit_datetime),
        )
      : []

  return {
    firstVisit: sortedVisits.length > 0 ? sortedVisits[0] : null,
    lastVisit: sortedVisits.length > 0 ? sortedVisits[sortedVisits.length - 1] : null,
  }
}

const createItemRow = (row) => {
  const nameItem = row.item_name_l || row.health_insurance_name || row.item_name_e
  const cleanedText = nameItem.replace(/[\u200B\u00A0\n\r]/g, ' ')
  const ham_luong = row.HAM_LUONG ?? ''

  return [
    cleanedText + ' ' + ham_luong,
    row.unit,
    row.quantity,
    row.unit_price ?? 0,
    '',
    row.health_insurance_price_with_ceiling ?? 0,
    '',
    '',
    row.service_rate || '100',
    '',
    '',
    row.total_after_tax ?? 0,
    row.payment_rate || '100',
    row.health_insurance_amount ?? 0,
    row.ss_cover ?? 0,
    row.BN_CUNG_CHI_TRA ?? 0,
    row.others ?? 0,
    row.BN_TU_CHI_TRA ?? 0,
  ]
}

const createItemKey = (row) => {
  const nameItem = row.item_name_l || row.health_insurance_name || row.item_name_e
  const cleanedText = nameItem.replace(/[\u200B\u00A0\n\r]/g, ' ')
  const ham_luong = row.HAM_LUONG ?? ''

  return `${cleanedText} ${ham_luong}|${row.unit}|${row.health_insurance_price_with_ceiling}|${row.unit_price}|${row.service_rate}|${row.payment_rate}`
}

const updateExistingItem = (existingItem, row) => {
  existingItem[2] += row.quantity // Add quantity
  existingItem[11] = existingItem[2] * row.unit_price // Recalculate total_after_tax
  existingItem[13] = existingItem[2] * (row.health_insurance_amount ?? 0) // Recalculate health_insurance_amount
  existingItem[14] = existingItem[2] * (row.ss_cover ?? 0) // Recalculate ss_cover
  existingItem[15] = existingItem[2] * (row.BN_CUNG_CHI_TRA ?? 0) // Recalculate BN_CUNG_CHI_TRA
  existingItem[16] = existingItem[2] * (row.others ?? 0) // Recalculate others
  existingItem[17] = existingItem[2] * (row.BN_TU_CHI_TRA ?? 0) // Recalculate BN_TU_CHI_TRA
}

const createTitleRow = (worksheet, startRow, title) => {
  const titleRow = worksheet.getRow(startRow)
  titleRow.values = [title, '', '', '', '', '', '', '', '', '', '', '', '']
  titleRow.alignment = { horizontal: 'left', wrapText: true }
  worksheet.mergeCells(`A${startRow}:K${startRow}`)

  // Apply styling to title row
  for (let col = 1; col <= 18; col++) {
    const cell = titleRow.getCell(col)
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'D9D9D9' },
    }
    cell.font = { bold: true, size: 10, name: 'Times New Roman' }
    cell.border = BORDER_STYLE
  }

  return titleRow
}

const addServiceDataRows = (worksheet, startRow, serviceData) => {
  let currentRow = startRow

  serviceData.forEach((serviceInfo) => {
    const row = worksheet.getRow(currentRow)
    row.alignment = { vertical: 'middle' }
    row.values = serviceInfo

    row.getCell(2).alignment = { horizontal: 'center', vertical: 'middle' }
    row.getCell(3).alignment = { horizontal: 'center', vertical: 'middle' }
    row.getCell(9).alignment = { horizontal: 'center', vertical: 'middle' }
    row.getCell(13).alignment = { horizontal: 'center', vertical: 'middle' }

    worksheet.mergeCells(`D${currentRow}:E${currentRow}`)
    worksheet.mergeCells(`F${currentRow}:H${currentRow}`)
    worksheet.mergeCells(`I${currentRow}:K${currentRow}`)

    currentRow++
  })

  return currentRow
}

const createSummaryData = (
  total_bv,
  total_qbhyt,
  total_bn_cung_chi_tra,
  total_bn_tu_chi_tra,
  total_khac,
) => {
  return [
    [
      'Tổng chi phí lần khám bệnh/cả đợt điều trị:',
      total_bv,
      '',
      'đồng',
      `(${numberToWordsVietnamese(total_bv)})`,
    ],
    ['Trong đó, số tiền do:'],
    [
      '- Quỹ BHYT thanh toán:',
      total_qbhyt,
      '',
      'đồng',
      `(${numberToWordsVietnamese(total_qbhyt)})`,
    ],
    [
      '- Người bệnh trả trong đó:',
      total_bv - total_qbhyt,
      '',
      'đồng',
      `(${numberToWordsVietnamese(total_bv - total_qbhyt)})`,
    ],
    [
      '  + Cùng trả trong phạm vi BHYT:',
      total_bn_cung_chi_tra,
      '',
      'đồng',
      `(${numberToWordsVietnamese(total_bn_cung_chi_tra)})`,
    ],
    [
      '  + Các khoản phải trả khác:',
      total_bn_tu_chi_tra,
      '',
      'đồng',
      `(${numberToWordsVietnamese(total_bn_tu_chi_tra)})`,
    ],
    ['- Nguồn khác:', total_khac, '', 'đồng', `(${numberToWordsVietnamese(total_khac)})`],
  ]
}

const createSignatureBlock = (currentDate, invoiceCreateFullName, sentCashierFullName) => {
  return [
    ['', '', '', '', '', '', '', '', '', '', '', '', '', currentDate],
    ['NGƯỜI LẬP BẢNG KÊ', '', '', '', '', '', '', '', '', '', '', '', '', 'KẾ TOÁN VIỆN PHÍ'],
    ['(ký, ghi rõ họ tên)', '', '', '', '', '', '', '', '', '', '', '', '', '(ký, ghi rõ họ tên)'],
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    [
      invoiceCreateFullName || '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      sentCashierFullName || '',
      '',
      '',
      '',
      '',
    ],
    ['XÁC NHẬN CỦA NGƯỜI BỆNH', '', '', '', '', '', '', '', '', '', '', '', '', currentDate],
    ['(ký, ghi rõ họ tên)', '', '', '', '', '', '', '', '', '', '', '', '', 'GIÁM ĐỊNH BHYT'],
    [
      '(Tôi đã nhận . . .  . . .phim Xquang/CT/MRI)',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '(ký, ghi rõ họ tên)',
    ],
  ]
}

const setupPatientInfo = (
  worksheet,
  inputDataPatientVisit,
  intputDataCardHealth,
  inputDataDisposition,
  inputDataMedicalCodingViewList,
  formatDate,
  formatTimeFromISO,
  firstVisitDateTime,
  lastInvoiceDateTime,
  visitDays,
) => {
  // department
  const departmentName = 'Khoa: ' + (inputDataPatientVisit?.department_ss_name ?? '')
  const departmentCode = 'Mã khoa: ' + (inputDataPatientVisit?.department_ss_code ?? '')
  setCellValueByName(worksheet.workbook, 'department_ss_name', departmentName)
  setCellValueByName(worksheet.workbook, 'department_ss_code', departmentCode)

  // (14) Trái tuyến
  const ss_trai_tuyen_check = ['WBC'].includes(inputDataPatientVisit?.referral_type_rcd) ? '☒' : '☐'
  setCellValueByName(worksheet.workbook, 'ss_trai_tuyen', `(14) Trái tuyến ${ss_trai_tuyen_check}`)

  const sex = inputDataPatientVisit.sex === 'Female' ? 'Nam ☐ Nữ ☒' : 'Nam ☒ Nữ ☐'

  // Mã BHYT parts
  const part1 = intputDataCardHealth?.[0]?.card_code.substring(0, 2)
  const part2 = intputDataCardHealth?.[0]?.card_code.substring(2, 3)
  const part3 = intputDataCardHealth?.[0]?.card_code.substring(3, 5)
  const part4 = intputDataCardHealth?.[0]?.card_code.substring(5)

  // Parse referral disposition
  let code, name
  let referral_disposition_name_l = intputDataCardHealth?.[0]?.referral_disposition_name_l

  if (referral_disposition_name_l?.startsWith('[') && referral_disposition_name_l?.includes(']')) {
    code = referral_disposition_name_l.substring(
      referral_disposition_name_l.indexOf('[') + 1,
      referral_disposition_name_l.indexOf(']'),
    )
    name = referral_disposition_name_l
      .substring(referral_disposition_name_l.indexOf(']') + 2)
      .trim()
  } else {
    code = ''
    name = ''
  }

  // Other patient info
  let Muc_11 = inputDataPatientVisit?.emergency_case_flag === true ? '☒' : '☐'
  let Muc_12 = inputDataPatientVisit?.right_channel_flag === true ? '☒' : '☐'
  let Muc_2 = intputDataCardHealth?.[0]?.card_address ?? ''
  let Noi_chuyen_den =
    inputDataDisposition.find(
      (item) => item.referral_disposition_rcd === inputDataPatientVisit.referral_disposition_rcd,
    )?.name_e ?? ''
  const Benh_Kem_Theo = inputDataMedicalCodingViewList.find((item) => item.primary_flag === false)
  const Benh_Chinh = inputDataMedicalCodingViewList.find((item) => item.primary_flag === true)

  let policy_coverage_percent = inputDataPatientVisit?.policy_coverage_percent ?? '0'
  let HN = inputDataPatientVisit?.visible_patient_id ?? ''

  // Set worksheet values
  worksheet.getRow(2).getCell(15).value = 'Mã số người bệnh: ' + HN

  const FullName = inputDataPatientVisit?.fullname ?? ''
  worksheet.getCell('A7').value = {
    richText: [
      {
        text: '(1) Họ tên người bệnh: ',
        font: { bold: false, name: 'Times New Roman', size: 10 },
      },
      {
        text: FullName.toUpperCase(),
        font: { bold: true, name: 'Times New Roman', size: 10 },
      },
    ],
  }

  worksheet.getRow(7).getCell(12).value =
    'Ngày, tháng, năm, sinh: ' + formatDate(inputDataPatientVisit.dob)
  worksheet.getRow(7).getCell(16).value = 'Giới tính: ' + sex
  worksheet.getRow(8).getCell(1).value = '(2) Địa chỉ hiện tại: ' + Muc_2

  // BHYT card info
  worksheet.getRow(9).getCell(2).value = part1
  worksheet.getRow(9).getCell(3).value = part2
  worksheet.getRow(9).getCell(4).value = part3
  worksheet.getRow(9).getCell(4).alignment = { horizontal: 'center' }
  worksheet.getRow(9).getCell(5).value = part4
  worksheet.getRow(9).getCell(12).value =
    'Giá trị từ ' +
    formatDate(intputDataCardHealth?.[0]?.effective_date) +
    ' Đến ' +
    formatDate(intputDataCardHealth?.[0]?.expiration_date)

  worksheet.getRow(10).getCell(1).value = '(5) Cơ sở đăng ký KCB BHYT ban đầu: ' + name
  worksheet.getRow(10).getCell(17).value = code

  // Visit dates
  worksheet.getRow(11).getCell(2).value =
    formatTimeFromISO(firstVisitDateTime) + ', ngày: ' + formatDate(firstVisitDateTime)
  worksheet.getRow(12).getCell(2).value =
    formatTimeFromISO(firstVisitDateTime) + ', ngày: ' + formatDate(firstVisitDateTime)

  let Ngay_ket_thuc_kham = formatDate(lastInvoiceDateTime)
  let Gio_ket_thuc_kham = formatTimeFromISO(lastInvoiceDateTime)
  worksheet.getRow(13).getCell(1).value =
    '(9) Kết thúc khám: ' + Gio_ket_thuc_kham + ', ngày: ' + Ngay_ket_thuc_kham
  worksheet.getRow(13).getCell(8).value = visitDays
  worksheet.getRow(13).getCell(18).value = inputDataPatientVisit.KET_QUA_DTRI

  worksheet.getRow(14).getCell(1).value = '(11) Cấp cứu ' + Muc_11 + '   (12) Đúng tuyến ' + Muc_12
  worksheet.getRow(14).getCell(2).value = 'Nơi chuyển đến từ: ' + Noi_chuyen_den + '; Nơi chuyển đi'

  // Medical diagnosis
  let Ten_benh_chinh = Benh_Chinh?.TEN_BENH_CHINH ?? ''
  worksheet.getRow(15).getCell(1).value = '(15) Chẩn đoán xác định: ' + Ten_benh_chinh
  worksheet.getRow(15).getCell(17).value = Benh_Chinh?.MA_BENH_CHINH

  let Ten_benh_kt = Benh_Kem_Theo?.TEN_BENH_KT ?? ''
  worksheet.getRow(16).getCell(1).value = '(17) Bệnh kèm theo: ' + Ten_benh_kt
  worksheet.getRow(17).getCell(17).value = Benh_Kem_Theo?.MA_BENH_KT

  // Additional dates
  worksheet.getRow(18).getCell(1).value =
    '(19) Thời điểm đủ 05 năm liên tục từ ngày: ' +
    formatDate(intputDataCardHealth?.[0]?.five_consecutive_years)
  worksheet.getRow(18).getCell(4).value =
    '(20) Miễn cùng chi trả trong năm từ ngày : ' +
    formatDate(inputDataPatientVisit?.free_copay_start_date)

  // Duplicate BHYT info
  worksheet.getRow(20).getCell(2).value = part1
  worksheet.getRow(20).getCell(3).value = part2
  worksheet.getRow(20).getCell(4).value = part3
  worksheet.getRow(20).getCell(4).alignment = { horizontal: 'center' }
  worksheet.getRow(20).getCell(5).value = part4
  worksheet.getRow(20).getCell(12).value =
    'Giá trị từ ' +
    formatDate(intputDataCardHealth?.[0]?.effective_date) +
    ' Đến ' +
    formatDate(intputDataCardHealth?.[0]?.expiration_date)
  worksheet.getRow(20).getCell(18).value = policy_coverage_percent + '%'
}

const SERVICE_TITLES = [
  { titleExcel: '1. Khám bệnh', includeCode: ['13'] },
  { titleExcel: '2. Ngày giường', includeCode: ['14', '15', '16'] },
  { titleExcel: '3. Xét Nghiệm', includeCode: ['1'] },
  { titleExcel: '4. Chẩn đoán hình ảnh', includeCode: ['2'] },
  { titleExcel: '5. Thăm dò chức năng', includeCode: ['3'] },
  { titleExcel: '6. Thủ Thuật, Phẫu Thuật', includeCode: ['8', '18'] },
  { titleExcel: '7. Máu, chế phẩm máu, vận chuyển máu', includeCode: ['7', '17'] },
  { titleExcel: '8. Thuốc và Dịch Truyền', includeCode: ['4'] },
  { titleExcel: '9. Vật tư y tế', includeCode: ['19'] },
  { titleExcel: '11. Vận chuyển người bệnh', includeCode: ['12'] },
  { titleExcel: '12. Dịch vụ khác', includeCode: ['0'] },
  { titleExcel: ' ', includeCode: [null, ''] }, // notfound ss_item_group_rcd
]

const BORDER_STYLE = {
  top: { style: 'thin' },
  left: { style: 'thin' },
  bottom: { style: 'thin' },
  right: { style: 'thin' },
}

export const processAndExportData = async (
  inputData,
  inputDataPatientVisit,
  intputDataCardHealth,
  inputDataDisposition,
  inputDataMedicalCodingViewList,
  sentCashierFullName,
  invoiceCreateFullName,
  invoiceCreateOn,
  selectedPatientVisitMappingViews = [],
) => {
  if (!inputData || inputData.length === 0) {
    return
  }

  const Data = inputData.flatMap((item) => item.children)
  const { firstVisit, lastVisit } = getVisitDates(selectedPatientVisitMappingViews)

  const workbook = new Workbook()
  const file = await fetch(config.HOME_PAGE + 'FormN.xlsx')
  const arrayBuffer = await file.arrayBuffer()
  await workbook.xlsx.load(arrayBuffer)
  const worksheet = workbook.getWorksheet('Sheet1')

  const formatDate = (isoString) => {
    if (!isoString) return ' . . . . /. . . ./. . . . .'

    const date = new Date(isoString)
    if (isNaN(date.getTime())) return ' . . . . /. . . ./. . . . .'

    const day = String(date.getDate()).padStart(2, '0')
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const year = date.getFullYear()

    return `${day}/${month}/${year}`
  }

  const formatTimeFromISO = (isoString) => {
    if (!isoString) return 'giờ . . . . .phút, ngày……/……………'

    try {
      const date = new Date(isoString)
      if (isNaN(date.getTime())) {
        return 'giờ . . . . .phút, ngày……/……………'
      }

      const formatter = new Intl.DateTimeFormat('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZone: 'Asia/Ho_Chi_Minh',
      })

      return formatter.format(date).replace(':', ' giờ ') + ' phút'
    } catch {
      return 'giờ . . . . .phút, ngày……/……………'
    }
  }

  const dateDiffInDays = (date1, date2) => {
    const d1 = new Date(date1)
    const d2 = new Date(date2)

    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0

    const date1Normalized = new Date(d1.getFullYear(), d1.getMonth(), d1.getDate())
    const date2Normalized = new Date(d2.getFullYear(), d2.getMonth(), d2.getDate())

    const diffInTime = date2Normalized.getTime() - date1Normalized.getTime()
    const diffInDays = diffInTime / (1000 * 60 * 60 * 24)

    return Math.abs(diffInDays)
  }

  // (7) Đến khám - always use earliest visit date from current selectedPatientVisitMappingViews
  // Ensure we're using the current selection's first visit date
  const firstVisitDateTime =
    firstVisit?.actual_visit_datetime || inputDataPatientVisit?.actual_visit_datetime

  // (9) Kết thúc lượt khám - determine the most appropriate end date
  // If we have multiple visits, use the last visit's date
  // If we have invoice creation date, use that
  // Otherwise fall back to the patient visit closure date
  let lastInvoiceDateTime

  if (lastVisit && firstVisit && lastVisit.patient_visit_id !== firstVisit.patient_visit_id) {
    // If we have multiple different visits, use the last visit's date
    lastInvoiceDateTime = lastVisit.closure_visit_datetime
  } else if (invoiceCreateOn) {
    // If we have invoice creation date, use that
    lastInvoiceDateTime = invoiceCreateOn
  } else {
    // Fall back to the patient visit
    lastInvoiceDateTime = inputDataPatientVisit?.actual_visit_datetime
  }

  // Calculate total treatment days: End date - Start date + 1
  // Ensure we're using the current selection's dates
  const visitDays = Math.ceil(dateDiffInDays(firstVisitDateTime, lastInvoiceDateTime)) + 1

  // Setup patient information
  setupPatientInfo(
    worksheet,
    inputDataPatientVisit,
    intputDataCardHealth,
    inputDataDisposition,
    inputDataMedicalCodingViewList,
    formatDate,
    formatTimeFromISO,
    firstVisitDateTime,
    lastInvoiceDateTime,
    visitDays,
  )

  //Nhóm dữ liệu theo tiêu đề
  const groupedData = {}
  Data.forEach((row) => {
    const groupCode = row?.ss_item_group_rcd ?? ''

    // Tìm title tương ứng dựa trên ss_item_group_rcd nằm trong includeCode
    const matchedTitle = SERVICE_TITLES.find((title) => title.includeCode.includes(groupCode))

    //Trường hợp ss_item_group_rcd rỗng hoặc = "10"
    if (groupCode === '10') {
      const defaultTitle = '10. Gói vật tư y tế'
      if (!groupedData[defaultTitle]) {
        groupedData[defaultTitle] = []
      }

      // Kiểm tra xem tất cả technical_services_cd_id đều null
      const allTechCdNull = Data.every((item) => item.technical_services_cd_id === null)

      // Nếu tất cả đều null → dùng mapping_technical_services_id, ngược lại dùng technical_services_cd_id
      const distinctMappingIds = [
        ...new Set(
          Data.map((item) =>
            allTechCdNull ? item.mapping_technical_services_id : item.technical_services_cd_id,
          ).filter(Boolean),
        ),
      ]

      const extractedData = distinctMappingIds
        .map((id) => {
          const item = Data.find((i) =>
            allTechCdNull
              ? i.mapping_technical_services_id === id
              : i.technical_services_cd_id === id,
          )
          return item
            ? {
                mapping_technical_services_id: id,
                NGAY_YL: item.NGAY_YL || 'N/A',
                charged_date_time: item.charged_date_time || 'N/A',
                mapping_technical_services_name: item.mapping_technical_services_name || 'N/A',
              }
            : null
        })
        .filter(Boolean)

      extractedData.forEach((item, index) => {
        const subTitle = `10.${index + 1} Gói vật tư y tế: ${
          item.mapping_technical_services_name
        } - Ngày: ${formatDate(item.charged_date_time)}`
        if (!groupedData[subTitle]) {
          groupedData[subTitle] = []
        }

        groupedData[subTitle].push(createItemRow(row))
      })
    } else if (matchedTitle) {
      const key = matchedTitle.titleExcel
      if (!groupedData[key]) {
        groupedData[key] = []
      }

      const itemKey = createItemKey(row)

      // Check if item already exists in the group
      const existingItemIndex = groupedData[key].findIndex(
        (item) => `${item[0]}|${item[1]}|${item[5]}|${item[3]}|${item[8]}|${item[12]}` === itemKey,
      )

      if (existingItemIndex !== -1) {
        // If item exists, add quantities and recalculate dependent values
        updateExistingItem(groupedData[key][existingItemIndex], row)
      } else {
        // If item doesn't exist, add it as new
        groupedData[key].push(createItemRow(row))
      }
    }
  })
  // console.log(groupedData)

  const insertRow = 23

  // worksheet.font = { name: 'Times New Roman' }
  let startRow = insertRow + 2
  // Tạo một danh sách các title đã có dữ liệu
  const existingTitles = new Set()

  // Duyệt và thêm dữ liệu cho các tiêu đề chính
  SERVICE_TITLES.forEach((originalTitle) => {
    if (groupedData[originalTitle.titleExcel]) {
      existingTitles.add(originalTitle.titleExcel) // Lưu lại tiêu đề đã thêm
      createTitleRow(worksheet, startRow, originalTitle.titleExcel)
      startRow++

      // Chèn dữ liệu theo nhóm
      startRow = addServiceDataRows(worksheet, startRow, groupedData[originalTitle.titleExcel])
    }
  })

  // 🔹 **Tìm các tiêu đề có dữ liệu nhưng chưa xuất hiện trong `normalizedTitles`**
  const missingTitles = Object.keys(groupedData).filter((title) => !existingTitles.has(title))

  // 🔹 **Thêm dữ liệu của các tiêu đề bị thiếu**
  missingTitles.forEach((missingTitle) => {
    createTitleRow(worksheet, startRow, missingTitle)
    startRow++

    // Chèn dữ liệu theo nhóm
    startRow = addServiceDataRows(worksheet, startRow, groupedData[missingTitle])
  })

  let total_bv = 0,
    total_bh = 0,
    total_qbhyt = 0,
    total_bn_cung_chi_tra = 0,
    total_bn_tu_chi_tra = 0,
    total_khac = 0

  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber > insertRow + 2 && rowNumber < startRow) {
      total_bv += Math.round(row.getCell(12).value || 0)
      total_bh += Math.round(row.getCell(14).value || 0)
      total_qbhyt += Math.round(row.getCell(15).value || 0)
      total_bn_cung_chi_tra += Math.round(row.getCell(16).value || 0)
      total_khac += Math.round(row.getCell(17).value || 0)
      total_bn_tu_chi_tra += Math.round(row.getCell(18).value || 0)
    }
  })

  const totalRow = worksheet.getRow(startRow)
  totalRow.values = [
    'Cộng:',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    total_bv,
    '',
    total_bh,
    total_qbhyt,
    total_bn_cung_chi_tra,
    total_khac,
    total_bn_tu_chi_tra,
  ]
  totalRow.font = { bold: true, size: 10, name: 'Times New Roman' }
  worksheet.mergeCells(`A${totalRow.number}:K${totalRow.number}`)

  for (let col = 1; col <= 18; col++) {
    totalRow.getCell(col).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } }
  }
  // totalRow.commit()
  // Chèn một dòng trống sau totalRow nếu chưa có
  const summaryStartRow = totalRow.number + 1
  worksheet.insertRow(summaryStartRow, [])

  const summaryData = createSummaryData(
    total_bv,
    total_qbhyt,
    total_bn_cung_chi_tra,
    total_bn_tu_chi_tra,
    total_khac,
  )

  // Bắt đầu chèn summaryData từ dòng kế tiếp
  let summaryRowIndex = summaryStartRow + 1

  summaryData.forEach((row) => {
    const fullText = row[0]
    const isTrongDo = typeof fullText === 'string' && fullText.startsWith('Trong đó')
    if (isTrongDo) {
      worksheet.insertRow(summaryRowIndex++, ['']) // dòng trống
      worksheet.getCell(`A${summaryRowIndex}`).value = {
        richText: [
          {
            text: 'Trong đó',
            font: { bold: true, name: 'Times New Roman', size: 10 },
          },
          {
            text: fullText.replace('Trong đó', ''),
            font: { bold: false, name: 'Times New Roman', size: 10 },
          },
        ],
      }
      summaryRowIndex++
    } else {
      const addedRow = worksheet.insertRow(summaryRowIndex++, row)
      const containsDong = row.some((cell) => typeof cell === 'string' && cell.includes('đồng'))
      if (containsDong) {
        worksheet.getRow(addedRow.number).getCell(5).font = {
          italic: true,
          name: 'Times New Roman',
        }
        worksheet.mergeCells(`B${addedRow.number}:C${addedRow.number}`)
      }
    }
  })

  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      if (rowNumber > totalRow.number && colNumber === 2) {
        cell.alignment = { horizontal: 'right', vertical: 'middle' }
        cell.font = { bold: true, size: 10, color: { argb: '000000' }, name: 'Times New Roman' }
        cell.numFmt = '#,##0'
      }
      if (
        (rowNumber === totalRow.number + 7 || rowNumber === totalRow.number + 8) &&
        colNumber === 2
      ) {
        cell.font = { bold: false, size: 10, color: { argb: '000000' }, name: 'Times New Roman' }
      }
      if ([4, 6, 12, 14, 15, 16, 17, 18].includes(colNumber)) {
        cell.numFmt = '#,##0'
      }

      if (rowNumber > insertRow + 2 && rowNumber < startRow) {
        if (colNumber === 1) {
          cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
        }
      }
    })

    if (rowNumber > insertRow + 1 && rowNumber <= startRow) {
      row.eachCell((cell, colNumber) => {
        if (colNumber <= 18) {
          cell.border = BORDER_STYLE
        }
      })
    }
  })

  // Get the end date for the signature - always use the current lastInvoiceDateTime
  // This ensures we're using the freshest data for each print operation
  let endDate = new Date(lastInvoiceDateTime)

  // Ensure we have a valid date
  if (!endDate || isNaN(endDate.getTime())) {
    endDate = new Date() // Fallback to current date if invalid
  }

  const day = String(endDate.getDate()).padStart(2, '0')
  const month = String(endDate.getMonth() + 1).padStart(2, '0') // Tháng bắt đầu từ 0
  const year = endDate.getFullYear()

  const currentDate = `Ngày ${day} tháng ${month} năm ${year}`

  const SIGN_BLOCK_START = 41
  startRow = startRow + 8

  if (startRow < SIGN_BLOCK_START) {
    const blankRows = SIGN_BLOCK_START - startRow
    for (let i = 0; i < blankRows; i++) {
      worksheet.addRow([])
    }
    startRow = SIGN_BLOCK_START
  } else {
    worksheet.addRow([])
  }

  const signBlock = createSignatureBlock(currentDate, invoiceCreateFullName, sentCashierFullName)

  // worksheet.addRow([]) // Thêm dòng trống
  signBlock.forEach((rowData, idx) => {
    const row = worksheet.addRow(rowData)
    worksheet.mergeCells(`N${row.number}:P${row.number}`)
    row.getCell(14).alignment = { horizontal: 'center', vertical: 'middle' }
    row.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' }
    row.eachCell((cell, colNumber) => {
      cell.value
      if (
        (idx === 1 && (colNumber === 1 || colNumber === 14)) ||
        (idx === 8 && (colNumber === 1 || colNumber === 14)) ||
        (idx === 9 && colNumber === 1) ||
        (idx === 10 && colNumber === 14)
      ) {
        cell.font = { bold: true, size: 8, name: 'Times New Roman' }
      }
      if (
        (idx === 2 && (colNumber === 1 || colNumber === 14)) ||
        (idx === 11 && colNumber === 14) ||
        (idx === 10 && colNumber === 1)
      ) {
        cell.font = { italic: true, size: 10, name: 'Times New Roman' }
      }
      // for fullname
      if ((idx === 8 && (colNumber === 1 || colNumber === 14)) || (idx === 0 && colNumber === 1)) {
        cell.font = { bold: true, size: 10, name: 'Times New Roman', color: { argb: 'FFFF0000' } }
      }
    })
  })

  for (let i = 0; i < 9; i++) {
    worksheet.addRow(new Array(worksheet.columnCount || 18).fill(''))
  }
  // const lastRow = worksheet.addRow([
  //   'Tôi đồng ý không hướng BHYT đối với thuốc, DVKT, VTYT ngoài phạm vi thanh toán của quỹ BHYT',
  // ])

  // lastRow.getCell(1).font = { bold: true, italic: true, name: 'Times New Roman', size: 10 }
  // lastRow.getCell(1).alignment = { wrapText: false }

  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'BangKe.xlsx'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
