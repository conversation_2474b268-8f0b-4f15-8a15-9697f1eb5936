import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Button,
  Checkbox,
  Form,
  Col,
  Row,
  Typography,
  DatePicker,
  Table,
  Tabs,
  Breadcrumb,
  Modal,
  Popconfirm,
  Collapse,
  Popover,
  Tag,
  Dropdown,
  Image,
} from 'antd'
const { RangePicker } = DatePicker
import { HistoryOutlined, MenuOutlined } from '@ant-design/icons'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useUI } from '../../common/UIProvider'
import { displayDate, displayDateTime, handleError, handleSetDateValue } from '../../common/helpers'
import {
  addMultiMergedPatientVisits,
  getApiPatientVisitMedicalCodingViewListByPatientVisitId,
  getSystemReferralDispositionRefs,
} from './VisitService'
import COLOR from '../../common/color'
import CheckConditionsTab from './CheckConditionsTab'
import TaskListTab from './TaskListTab'
import ChargeDetailsTab from './ChargeDetailsTab'
import InsuranceInvoicesTab from './InsuranceInvoicesTab'
import XMLTablesListTab from './XMLTablesListTab'
import imgPatient from '../../assets/patient-icon.png'
import { TABLE_MERGE_VISIT_COLUMNS, PROCESSING_STATUS, ACTION_VISIT_HISTORY } from './VisitConstant'
import { useDispatch, useSelector } from 'react-redux'
import { MODULE_VISIT, visitActions } from '../../store/Visit'
import { BUTTON_FONT_WEIGHT, FORM_MODE, FORMAT_DATE, MODE_VIEW_DATA } from '../../common/constant'
import PolicySubscriptionPopup from './PolicySubscriptionPopup'
import SearchAndAddVisitPopup from './SearchAndAddVisitPopup'
import MergeVisitDatasetTable from './MergeVisitDatasetTable'
import { useWatch } from 'antd/es/form/Form'
import { MODULE_AUTH } from '../../store/auth'
import {
  deleteListItemService,
  getItemsService,
  updateListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import VisitInformationPopup from './VisitInformationPopup'
import OTMSListTab from './OTMSListTab'
import { usePatientVisit } from './hooks/usePatientVisit'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import VisitTrackingHistoryPopup from './VisitTrackingHistoryPopup'
import endpoints from '../../common/endpoints'
import PatientMedicalCodingForm from './PatientMedicalCodingForm'
import { isEqual } from 'lodash'
import useDeepCompareEffect, { useDeepCompareMemoize } from 'use-deep-compare-effect'
import PDFFormExample from '../../common/hooks/usePopulatePDF.example'

const { Text } = Typography

const NinhPage = () => {
  // Complex object with nested structure

  return (
    <div>
      <PDFFormExample />
    </div>
  )
}

export default NinhPage
