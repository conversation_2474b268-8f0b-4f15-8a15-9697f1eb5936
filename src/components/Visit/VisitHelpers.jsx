// src/components/Visit/VisitHelpers.jsx

import {
  PROCESSING_STATUS,
  TABLE_COVERED_CHARGE_SUMMARY_COLUMNS,
  STATUS_ORDER,
  WARNING_STATUS,
  VALID_INSURANCE_VALID_CODES,
  REFERRAL_DISPOSITION,
} from './VisitConstant'
import {
  convertUTCToLocalTime,
  displayCurrency,
  formatDateString,
  handleError,
  handleSetDateValue,
  roundCurrency,
} from '../../common/helpers'
import dayjs from '../../common/dayjs'
import lists from '../../common/lists'
import { getItemsService, updateListItemService } from '../../common/services'
import {
  getTechnicalServicesByMedicalSuppliesId,
  getTechnicalServicesByMedicinesId,
} from './VisitService'
import { FORM_MODE, MODE_VIEW_DATA } from '../../common/constant'
import { PERMISSION } from '../Auth/AuthConstant'

export const groupDataByInvoiceTransaction = (input) => {
  const groupedData = {}

  input.forEach((item, index) => {
    const transactionKey = item.ar_invoice_transaction_text

    if (!groupedData[transactionKey]) {
      groupedData[transactionKey] = []
    }

    // Add the item to the group, including a `key` field based on the index
    groupedData[transactionKey].push({
      ...item,
      id: item.item_code,
      key: index,
      // charge_ss_cover: item.ss_cover - 1, // TODO : test case charge_ss_cover !== ss_cover
      invoice_transaction_with_item_name: item.item_name_l || item.item_name_e,
      warning_status: (
        <span className="d-flex align-items-center">{WARNING_STATUS.iconMap.warning}</span>
      ),
    })
  })

  // Convert the grouped object into an array format and calculate totals
  return Object.keys(groupedData).map((transactionKey) => {
    const children = groupedData[transactionKey]

    // Calculate totals for the group
    const charge_ss_cover = children.reduce((sum, item) => sum + (item.charge_ss_cover || 0), 0)
    const ss_cover = children.reduce((sum, item) => sum + (item.ss_cover || 0), 0)
    const unit_price = children.reduce((sum, item) => sum + (item.unit_price || 0), 0)
    const health_insurance_price_with_ceiling = children.reduce(
      (sum, item) => sum + (item.health_insurance_price_with_ceiling || 0),
      0,
    )
    const total_after_tax = children.reduce((sum, item) => sum + (item.total_after_tax || 0), 0)
    const health_insurance_amount = children.reduce(
      (sum, item) => sum + (item.health_insurance_amount || 0),
      0,
    )
    const BN_CUNG_CHI_TRA = children.reduce((sum, item) => sum + (item.BN_CUNG_CHI_TRA || 0), 0)
    const BN_TU_CHI_TRA = children.reduce((sum, item) => sum + (item.BN_TU_CHI_TRA || 0), 0)

    return {
      key: transactionKey,
      id: transactionKey,
      invoice_transaction_with_item_name: transactionKey,
      children: children,
      charge_ss_cover: charge_ss_cover,
      ss_cover: ss_cover,
      unit_price: unit_price,
      health_insurance_price_with_ceiling: health_insurance_price_with_ceiling,
      total_after_tax: total_after_tax,
      health_insurance_amount: health_insurance_amount,
      BN_CUNG_CHI_TRA: BN_CUNG_CHI_TRA,
      BN_TU_CHI_TRA: BN_TU_CHI_TRA,
      MA_LOAI_KCB: children[0]?.MA_LOAI_KCB,
      patient_visit_id: children[0]?.patient_visit_id,
    }
  })
}

export const groupByStatus = (data) => {
  const groupedData = {}

  data.forEach((item, index) => {
    const statusKey = item.processing_status
    const statusLabel = PROCESSING_STATUS[statusKey]?.name_l || 'Không xác định'

    if (!groupedData[statusKey]) {
      groupedData[statusKey] = {
        key: `parent-${statusKey}`,
        processing_status: statusLabel,
        isParent: true,
        children: [],
      }
    }

    groupedData[statusKey].children.push({
      key: index,
      numberOrder: index + 1,
      processing_status: '',
      ...item,
    })
  })

  Object.values(groupedData).forEach((group) => {
    const totalChildren = group.children.length
    group.processing_status += ` (${totalChildren})`
  })

  const sortedGroups = Object.keys(groupedData)
    .sort((a, b) => {
      const indexA = STATUS_ORDER.indexOf(a)
      const indexB = STATUS_ORDER.indexOf(b)
      return (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB)
    })
    .map((key) => groupedData[key])

  return sortedGroups
}

export const updateStatus = async (dataTableXmlTracking, selectedSsTable1) => {
  const filteredData = dataTableXmlTracking.filter(
    (item) => item.table_1_id === selectedSsTable1?.table_1_id,
  )

  // Danh sách các bảng cần kiểm tra và cập nhật thêm data nếu cần
  const tableNames = [
    'ss_table_1',
    'ss_table_2',
    'ss_table_3',
    'ss_table_4',
    'ss_table_5',
    'ss_table_7',
  ]

  // Lọc ra các bảng trong danh sách trên
  const filteredTables = filteredData.filter((item) => tableNames.includes(item.table_name))

  if (filteredTables.length > 0) {
    const hasNullStatus = filteredTables.some((item) => !item._status || item._status === 'null')

    if (hasNullStatus) {
      await updateListItemService(
        lists.ss_table_1,
        selectedSsTable1?.[lists.ss_table_1.primaryKeyName],
        {
          approve_status: 'pending',
        },
      )
    } else {
      await updateListItemService(
        lists.ss_table_1,
        selectedSsTable1?.[lists.ss_table_1.primaryKeyName],
        {
          approve_status: 'approved',
        },
      )
    }
  }
}

export const groupChargeDetailsByItemGroup = (input, isSSCoverFlag = true) => {
  const groupedData = {}

  input.forEach((item, index) => {
    const transactionKey = isSSCoverFlag
      ? item?.item_group_name_l || item?.top_item_group_name_l
      : item?.top_item_group_name_l || ''

    if (!groupedData[transactionKey]) {
      groupedData[transactionKey] = []
    }

    // Add the item to the group, including a `key` field based on the index
    groupedData[transactionKey].push({
      ...item,
      id: item.item_name_e,
      item_name: item.item_name_e || '',
      title_group_name: item.item_name_e || '',
      key: index, // Add `key` column using the row index
      warning_status: (
        <span className="d-flex align-items-center">{WARNING_STATUS.iconMap.warning}</span>
      ),
    })
  })

  // List of fields to calculate totals for
  const totalFields = TABLE_COVERED_CHARGE_SUMMARY_COLUMNS

  // Convert the grouped object into an array format
  return Object.keys(groupedData).map((transactionKey) => {
    const children = groupedData[transactionKey]
    const groupTotals = calculateGroupTotals(children, totalFields)

    return {
      key: transactionKey,
      id: transactionKey,
      title_group_name: transactionKey || '',
      unit_price: 'N/A',
      health_insurance_unit_price_before_tax: 'N/A',
      health_insurance_price_with_ceiling: 'N/A',
      payment_ceiling: 'N/A',
      purchased_price: 'N/A',
      medicine_id: 'N/A',
      medical_supplies_id: 'N/A',
      ...groupTotals,
      children: groupedData[transactionKey], // Add grouped children
    }
  })
}

/**
 * status === 0 'Chưa có DVKT tương ứng được charge'
 * status === 1 'Vui lòng chọn DVKT tương ứng'
 * status === 3 'DVKT chưa được move up'
 */
export const handleCheckMappedChargeDetailItems = (
  record,
  visitChargeDetailList,
  ssItemTechnicalServiceMapping,
) => {
  const mappedChargeDetailItems = []

  for (const service of ssItemTechnicalServiceMapping) {
    mappedChargeDetailItems.push(
      ...visitChargeDetailList.filter(
        (detail) => detail.technical_services_id === service.technical_services_id,
      ),
    )
  }

  if (mappedChargeDetailItems.length === 0) {
    return { status: 0, mappedChargeDetailItems }
  } else if (mappedChargeDetailItems.length >= 2 && !record?.technical_services_cd_id) {
    return { status: 1, mappedChargeDetailItems }
  } else if (mappedChargeDetailItems[0]?.manual_ss_cover_flag) {
    return { status: 2, mappedChargeDetailItems }
  } else if (ssItemTechnicalServiceMapping.length > 0) {
    return { status: 3, mappedChargeDetailItems }
  }

  return -1
}

export const calculateGroupTotals = (children, fields) =>
  fields.reduce((totals, field) => {
    totals[field] = children.reduce((sum, child) => sum + (child[field] || 0), 0)
    return totals
  }, {})

// #region health_insurance_card
export const mapPortalInsuranceCardsToHealthInsuranceCards = (
  portalInsuranceCards,
  systemReferralDispositionRefs,
) => {
  const mappedHealthInsuranceCards = portalInsuranceCards.map((card) => {
    const maDkbd = card?.maDKBD
    const systemReferralDisposition = systemReferralDispositionRefs.find((item) =>
      item.name_e.includes(`[${maDkbd}]`),
    )
    return {
      health_insurance_card_id: card?.healthInsuranceCardId || null,
      key: card?.key,
      card_code: card?.maThe || card?.initialCardCode,
      card_address: card?.diaChi,
      issue_date: formatDateString(card?.gtTheTu),
      effective_date: formatDateString(card?.gtTheTu),
      expiration_date: formatDateString(card?.gtTheDen),
      five_consecutive_years: formatDateString(card?.ngayDu5Nam),
      referral_disposition_rcd: systemReferralDisposition?.referral_disposition_rcd,
      insurance_card_check_details: card?.ghiChu,
      maKetQua: card?.maKetQua,
    }
  })
  return mappedHealthInsuranceCards
}

export const setFormListInsuranceCards = (form, mappedHealthInsuranceCards, regions = []) => {
  const formListInsuranceCards = mappedHealthInsuranceCards.map((card, index) => {
    return {
      ...mapHealthInsuranceCardToFormInsuranceCard(card, regions),
      key: index,
    }
  })
  form.setFieldsValue({
    insuranceCards: formListInsuranceCards,
  })
  return formListInsuranceCards
}
export const mapHealthInsuranceCardToFormInsuranceCard = (healthInsuranceCard, regions) => {
  let disabledFields = []
  if (VALID_INSURANCE_VALID_CODES.includes(healthInsuranceCard?.maKetQua)) {
    disabledFields = Object.keys(healthInsuranceCard).filter((key) => healthInsuranceCard[key])
  }
  let region = healthInsuranceCard?.region_id
  if (!region) {
    region = getRegionByCardCode(healthInsuranceCard?.card_code, regions)?.region_id
  }

  return {
    healthInsuranceCardId: healthInsuranceCard?.health_insurance_card_id,
    address: healthInsuranceCard?.card_address,
    cardCode: healthInsuranceCard?.card_code,
    // For cards with codes 1 and 5, don't set continuousYears if it's null
    continuousYears: handleSetDateValue(healthInsuranceCard?.five_consecutive_years),
    effectiveDate: [
      handleSetDateValue(healthInsuranceCard?.effective_date),
      handleSetDateValue(healthInsuranceCard?.expiration_date),
    ],
    issueDate: handleSetDateValue(healthInsuranceCard?.issue_date),
    cardCheckDetails: healthInsuranceCard?.insurance_card_check_details,
    patientId: healthInsuranceCard?.patient_id,
    registerPlace: healthInsuranceCard?.referral_disposition_rcd,
    region,
    subRegion: healthInsuranceCard?.subregion_id,
    wardSubRegion: healthInsuranceCard?.ward_display_code,
    note: healthInsuranceCard?.note,
    disabledFields,
    maKetQua: healthInsuranceCard?.maKetQua,
  }
}
export const updateRawHealthInsuranceCards = (
  formListInsuranceCards,
  checkedHealthInsuranceCards,
) => {
  return formListInsuranceCards.map((formCard) => {
    const checkedCard = checkedHealthInsuranceCards.find(
      (checkedCard) =>
        // checkedCard.card_code === formCard.cardCode &&
        checkedCard.key === formCard.key,
    )
    if (checkedCard) {
      return { ...checkedCard }
    }
    return mapFormInsuranceCardToHealthInsuranceCard(formCard)
  })
}

export const mapFormInsuranceCardToHealthInsuranceCard = (formCard) => {
  // Check if this is a card with code 1 or 5
  const isSpecialCard =
    formCard?.cardCode &&
    (formCard.cardCode.charAt(2) === '1' || formCard.cardCode.charAt(2) === '5')

  return {
    health_insurance_card_id: formCard?.healthInsuranceCardId,
    card_code: formCard?.cardCode,
    card_address: formCard?.address,
    issue_date: formCard?.issueDate,
    effective_date: formCard?.effectiveDate?.[0],
    expiration_date: formCard?.effectiveDate?.[1],
    // For cards with codes 1 and 5, five_consecutive_years can be null
    five_consecutive_years: isSpecialCard ? null : formCard?.continuousYears,
    referral_disposition_rcd: formCard?.registerPlace,
    insurance_card_check_details: formCard?.cardCheckDetails,
    region_id: formCard?.region,
    subregion_id: formCard?.subRegion,
    ward_display_code: formCard?.wardSubRegion,
    note: formCard?.note,
    key: formCard?.key,
    maKetQua: formCard?.maKetQua,
  }
}

export const getDistinctCardCodes = (data) => {
  return Object.values(
    data.value.reduce((acc, item) => {
      if (!acc[item.card_code]) {
        acc[item.card_code] = item
      }
      return acc
    }, {}),
  )
}

// export const getRegionInfoFromCardCode = (cardCode, regionNlViews) => {
//   if (!cardCode || cardCode.length < 2) {
//     console.error('Invalid card code')
//     return null
//   }

//   // Lấy 2 ký tự đầu tiên từ mã thẻ bảo hiểm y tế
//   const regionCode = cardCode.substring(3, 5)
//   // Tra cứu thông tin tỉnh trong danh sách regionNlViews
//   const regionInfo = regionNlViews.find((region) => region.display_code == regionCode)

//   if (!regionInfo) {
//     console.error(`Không tìm thấy thông tin tỉnh với mã: ${regionCode}`)
//     return null
//   }

//   return regionInfo
// }

export const addressExtractor = (address) => {
  if (!address || address.toLowerCase() === 'null') {
    return null
  }

  // Tách địa chỉ thành mảng các phần
  const parts = address
    .split(',')
    .map((part) => part.trim())
    .filter((part) => part)

  // Lấy 3 phần cuối cùng
  const wardName = parts[parts.length - 3] || null
  const subRegionName = parts[parts.length - 2] || null
  const regionName = parts[parts.length - 1] || null

  return { wardName, subRegionName, regionName }
}

export const filterValidHealthInsuranceCards = (healthInsuranceCards) => {
  const today = dayjs() // Lấy ngày hôm nay
  return healthInsuranceCards.filter((card) => {
    // Chuyển đổi effective_date và expiration_date thành đối tượng dayjs
    const effectiveDate = card.effective_date ? dayjs(card.effective_date) : null
    const expirationDate = card.expiration_date ? dayjs(card.expiration_date) : null

    // Kiểm tra nếu effectiveDate hoặc expirationDate không hợp lệ
    if (!effectiveDate?.isValid() || !expirationDate?.isValid()) {
      console.error('Ngày không hợp lệ:', { effectiveDate, expirationDate })

      return false // Loại bỏ thẻ nếu thiếu ngày hiệu lực hoặc ngày hết hạn
    }

    // Kiểm tra điều kiện ngày
    return effectiveDate.isSameOrBefore(today, 'day') && expirationDate.isSameOrAfter(today, 'day')
  })
}

// #region patient_visit
export const mapSelectedFormInsuranceCardsToHealthInsuranceCards = (
  selectedFormListInsuranceCards,
  patientVisitId,
  currentUser,
  systemReferralDispositionRefs,
  currentPatient,
) => {
  return selectedFormListInsuranceCards.map((card) => {
    const systemReferralDisposition = systemReferralDispositionRefs.find(
      (item) => item?.referral_disposition_rcd == card?.registerPlace,
    )
    return {
      // ...matchedCard, //note: matchedCard includes: health_insurance_card_id] and others fields
      health_insurance_card_id: card?.healthInsuranceCardId || null,
      card_code: card?.cardCode,
      card_address: card?.address,
      issue_date: convertUTCToLocalTime(card?.issueDate),
      effective_date: convertUTCToLocalTime(card?.effectiveDate?.[0]),
      expiration_date: convertUTCToLocalTime(card?.effectiveDate?.[1]),
      five_consecutive_years: convertUTCToLocalTime(card?.continuousYears),
      referral_disposition_name_l: systemReferralDisposition?.name_l,
      referral_disposition_rcd: card?.registerPlace,
      patient_visit_id: patientVisitId || null,
      patient_id: currentPatient?.Patient_id,
      region_id: card?.region,
      subregion_id: card?.subRegion,
      ward_display_code: card?.wardSubRegion,
      note: card?.note,
      insurance_card_check_details: card?.cardCheckDetails,
      lu_user_id: currentUser?.User_id,
    }
  })
}

export const processPatientVisitData = (
  formValues,
  systemReferralTypeRefs,
  currentUser,
  currentPatient,
  selectedPatientVisitId,
  selectedSeriousIllness,
) => {
  const {
    ethnicity,
    cardType,
    designatedIllnessReason,
    emergencyCaseFlag,
    freeCopayFlag,
    medicalRecordCode,
    note,
    occupation,
    referralCode,
    referralDate,
    referralDestination,
    referralReason,
    referralType,
    freeCopayDate,
    // eslint-disable-next-line no-unused-vars
    seriousIllness,
    // eslint-disable-next-line no-unused-vars
    seriousIllnessCode,
    seriousIllnessFlag,
    signedHealthInsuranceConsentFormFlag,
  } = formValues
  const isRightChannel = isRightChannelReferral(systemReferralTypeRefs, referralType)

  return {
    card_type_id: cardType || null,
    designated_illness_reason: (referralType == 'SII' && designatedIllnessReason) || null,
    emergency_case_flag: emergencyCaseFlag,
    ethnicity_id: ethnicity || null,
    free_copay_flag: freeCopayFlag || null,
    lu_updated: dayjs(),
    lu_user_id: currentUser?.User_id,
    medical_record_code: medicalRecordCode || null,
    note: note || null,
    occupation_id: occupation || null,
    patient_id: currentPatient?.Patient_id,
    patient_visit_id: selectedPatientVisitId,
    referral_code: referralCode || null,
    referral_date: referralDate || null,
    referral_disposition_rcd: referralDestination || null,
    referral_reason: referralReason || null,
    referral_type_rcd: referralType || null,
    free_copay_end_date: freeCopayDate?.[1] || null,
    free_copay_start_date: freeCopayDate?.[0] || null,
    right_channel_flag: isRightChannel,
    serious_illness_flag: seriousIllnessFlag || null,
    //serious_illness_id: seriousIllnessFlag ? seriousIllness : null
    serious_illness_icd10id: selectedSeriousIllness?.icd10id,
    serious_illness_icd_code: selectedSeriousIllness?.code,
    signed_health_insurance_consent_form_flag: signedHealthInsuranceConsentFormFlag || false,
  }
}

export const initializeExpandedKeys = (insuranceCards) => {
  const initExpandedKeys = {}
  insuranceCards?.forEach((_, index) => {
    initExpandedKeys[index] = true // Set to false to not show the first row by default
  })
  return initExpandedKeys
}

export const RIGH_CHANNEL_REF = ['TRC', 'RRC', 'CB7', 'SII']
export const isRightChannelReferral = (systemReferralTypeRefs, referralType) => {
  const referral_type_record = systemReferralTypeRefs.find(
    (ref) => ref.referral_type_rcd === referralType,
  )
  return RIGH_CHANNEL_REF.includes(referral_type_record?.referral_type_rcd)
}

const calculateChargeDetailsRaw = (visitChargeDetails) => {
  if (!visitChargeDetails) {
    return []
  }

  // Round total_after_tax once at the beginning
  let processedVisitChargeDetails = visitChargeDetails.map((item) => {
    // default value
    const TYLE_TAI_SD = item.TYLE_TAI_SD || 100
    const payment_rate = item.payment_rate || 100
    const service_rate = item.service_rate || 100

    // Calculate unit_price only if needed
    const newUnitPrice =
      item.unit_price > 1000 && !item.calculated // calculated = true at the last of this func
        ? Math.round(item.unit_price * (TYLE_TAI_SD / 100))
        : item.unit_price
    const newSSPriceBefroreTax = item.calculated
      ? item.health_insurance_unit_price_before_tax
      : Math.round(item.health_insurance_unit_price_before_tax * (TYLE_TAI_SD / 100))

    return {
      ...item,
      total_after_tax: Math.round(item.total_after_tax),
      unit_price: newUnitPrice,
      rawUnitPrice: item.calculated ? item.rawUnitPrice : item.unit_price,
      health_insurance_unit_price_before_tax: newSSPriceBefroreTax,
      TYLE_TAI_SD,
      payment_rate,
      service_rate,
    }
  })

  // sort by unit_price
  processedVisitChargeDetails = processedVisitChargeDetails.sort(
    (a, b) => b.unit_price - a.unit_price,
  )

  // Filter items with manual_ss_cover_flag and group them
  const manualSsCoverItems = processedVisitChargeDetails.filter((item) => item.manual_ss_cover_flag)
  const groupedData = groupDataVTYT(manualSsCoverItems)

  // Get base values from any item (assuming all items have the same values)
  const firstItem =
    Object.values(groupedData)[0]?.true?.[0]?.items?.[0] ||
    Object.values(groupedData)[0]?.false?.[0] ||
    {}

  const policy_coverage_percent = firstItem.policy_coverage_percent || 0
  const base_45_months_salary = firstItem.base_45_months_salary || 0

  // Process all items in a single pass
  const processedGroupedData = {}

  // Calculate initial values for all items
  Object.entries(groupedData).forEach(([mappingId, perUseGroups]) => {
    processedGroupedData[mappingId] = { true: {}, false: [] }

    // Process items with per_use_flag = true
    if (perUseGroups.true) {
      Object.entries(perUseGroups.true).forEach(([maNhomKey, maNhomGroup]) => {
        processedGroupedData[mappingId].true[maNhomKey] = {
          count: maNhomGroup.count,
          hasLan1: maNhomGroup.hasLan1,
          items: [],
        }

        maNhomGroup.items.forEach((item) => {
          // Calculate health_insurance_price_with_ceiling
          let paymentCeilingToCheck = item.payment_ceiling
          if (item.usageLabel === 'Lan 2') {
            // Find the corresponding Lan 1 item
            const lan1Item = maNhomGroup.items.find(
              (i) => i.MA_NHOM_VAT_TU === item.MA_NHOM_VAT_TU && i.usageLabel === 'Lan 1',
            )
            paymentCeilingToCheck = lan1Item?.payment_ceiling || item.payment_ceiling
          }

          const validPrices = [
            paymentCeilingToCheck,
            item.health_insurance_unit_price,
            item.rawUnitPrice,
            item.purchased_price,
          ].filter((p) => !!p && p > 1000)

          let health_insurance_price_with_ceiling = // TODO: check me
            validPrices.length > 0 ? Math.min(...validPrices) : 0

          health_insurance_price_with_ceiling = Math.round(
            health_insurance_price_with_ceiling * (item.TYLE_TAI_SD / 100),
          )

          // Calculate health_insurance_amount
          const health_insurance_amount = roundCurrency(
            health_insurance_price_with_ceiling *
              item.quantity *
              (item.service_rate / 100) *
              (item.payment_rate / 100),
            2,
          )

          // Calculate ss_cover_raw
          const ss_cover_raw = (health_insurance_amount * item.policy_coverage_percent) / 100

          processedGroupedData[mappingId].true[maNhomKey].items.push({
            ...item,
            health_insurance_price_with_ceiling,
            health_insurance_amount,
            ss_cover_raw,
          })
        })
      })
    }

    // Process items with per_use_flag = false
    if (perUseGroups.false) {
      processedGroupedData[mappingId].false = perUseGroups.false.map((item) => {
        // Calculate health_insurance_price_with_ceiling
        const validPrices = [
          item.payment_ceiling,
          item.health_insurance_unit_price,
          item.rawUnitPrice,
          item.purchased_price,
        ].filter((p) => !!p && p > 1000)

        let health_insurance_price_with_ceiling =
          validPrices.length > 0 ? Math.min(...validPrices) : 0
        health_insurance_price_with_ceiling = Math.round(
          health_insurance_price_with_ceiling * (item.TYLE_TAI_SD / 100),
        )

        // Calculate health_insurance_amount
        const health_insurance_amount = roundCurrency(
          health_insurance_price_with_ceiling *
            item.quantity *
            (item.service_rate / 100) *
            (item.payment_rate / 100),
          2,
        )

        // Calculate ss_cover_raw
        const ss_cover_raw = (health_insurance_amount * item.policy_coverage_percent) / 100

        return {
          ...item,
          health_insurance_price_with_ceiling,
          health_insurance_amount,
          ss_cover_raw,
        }
      })
    }
  })

  // Calculate adjustment factors and apply final calculations
  Object.values(processedGroupedData).forEach((perUseGroups) => {
    // Calculate ss_cover_raw_total for this group
    let ss_cover_raw_total = 0

    if (perUseGroups.true) {
      Object.values(perUseGroups.true).forEach((maNhomGroup) => {
        maNhomGroup.items.forEach((item) => {
          if (item.usageLabel !== 'Lan 2' && item.usageLabel !== '') {
            ss_cover_raw_total += item.ss_cover_raw
          }
        })
      })
    }

    if (perUseGroups.false) {
      perUseGroups.false.forEach((item) => {
        ss_cover_raw_total += item.ss_cover_raw
      })
    }

    // Calculate adjustmentFactor for this group
    const policyCoverageLimit = base_45_months_salary * (policy_coverage_percent / 100)
    const adjustmentFactor =
      ss_cover_raw_total > policyCoverageLimit && policyCoverageLimit > 0
        ? policyCoverageLimit / ss_cover_raw_total
        : 1.0

    // Apply adjustmentFactor to true items
    if (perUseGroups.true) {
      Object.values(perUseGroups.true).forEach((maNhomGroup) => {
        maNhomGroup.items = maNhomGroup.items.map((item) => {
          // Calculate newPaymentCeiling
          let newPaymentCeiling = item.payment_ceiling

          if (item.usageLabel === 'Lan 2') {
            // Find the corresponding Lan 1 item
            const lan1Item = maNhomGroup.items.find(
              (i) => i.MA_NHOM_VAT_TU === item.MA_NHOM_VAT_TU && i.usageLabel === 'Lan 1',
            )

            if (lan1Item) {
              newPaymentCeiling = lan1Item.health_insurance_price_with_ceiling / 2
            }
          }

          // Calculate ss_cover based on usageLabel
          let ss_cover

          if (item.usageLabel === '2 Lan') {
            ss_cover =
              item.ss_cover_raw * adjustmentFactor + item.health_insurance_price_with_ceiling / 2
          } else if (item.usageLabel === 'Lan 2') {
            const validPrices = [
              item.payment_ceiling,
              item.health_insurance_unit_price,
              item.rawUnitPrice,
              item.purchased_price,
              item.health_insurance_price_with_ceiling,
            ].filter((p) => !!p)

            ss_cover = validPrices.length > 0 ? Math.min(...validPrices) : 0
          } else {
            // Lan 1 or empty usageLabel
            ss_cover = item.ss_cover_raw * adjustmentFactor
          }

          return {
            ...item,
            payment_ceiling: newPaymentCeiling,
            ss_cover: roundCurrency(ss_cover, 2),
          }
        })
      })
    }

    // Apply adjustmentFactor to false items
    if (perUseGroups.false) {
      perUseGroups.false = perUseGroups.false.map((item) => ({
        ...item,
        ss_cover: roundCurrency(item.ss_cover_raw * adjustmentFactor, 2),
      }))
    }
  })

  // Flatten the processed grouped data
  let flattenedData = []
  Object.values(processedGroupedData).forEach((perUseGroups) => {
    if (perUseGroups.true) {
      Object.values(perUseGroups.true).forEach((maNhomGroup) => {
        flattenedData.push(...maNhomGroup.items)
      })
    }
    if (perUseGroups.false) {
      flattenedData.push(...perUseGroups.false)
    }
  })

  // Merge the flattened data back into the original visitChargeDetails
  let result = processedVisitChargeDetails.map(
    (item) =>
      flattenedData.find((d) => d.visit_charge_detail_id === item.visit_charge_detail_id) || item,
  )

  // Check if all technical_services_cd_id are null
  const allTechCdNull = result.every((item) => item.technical_services_cd_id === null)

  // Get distinct mapping IDs
  const idField = allTechCdNull ? 'mapping_technical_services_id' : 'technical_services_cd_id'
  const distinctMappingIds = [...new Set(result.map((item) => item[idField]).filter(Boolean))]

  // Assign GOI_VTYT values in a single pass
  distinctMappingIds.forEach((id, index) => {
    const gName = `G${index + 1}`
    result = result.map((item) => ({
      ...item,
      GOI_VTYT: item[idField] === id ? gName : item.GOI_VTYT || '',
    }))
  })

  // Calculate final values in a single pass
  result = result.map((item) => {
    const total_after_tax = item.unit_price * item.quantity
    const BN_CUNG_CHI_TRA =
      item.usageLabel === 'Lan 2'
        ? 0
        : roundCurrency(item.health_insurance_amount - item.ss_cover, 2)
    const BN_TU_CHI_TRA =
      item.usageLabel === 'Lan 2'
        ? roundCurrency(item.total_after_tax - item.ss_cover, 2)
        : roundCurrency(item.total_after_tax - item.health_insurance_amount, 2)
    const TT_BN_CHI_TRA = BN_CUNG_CHI_TRA + BN_TU_CHI_TRA

    const price_difference =
      (item.purchased_price -
        Math.round((item.health_insurance_price_with_ceiling * 100) / item.TYLE_TAI_SD)) *
      item.quantity

    const calculatedTimes = item.calculatedTimes ? item.calculatedTimes + 1 : 1

    return {
      ...item,
      total_after_tax,
      BN_CUNG_CHI_TRA,
      BN_TU_CHI_TRA,
      TT_BN_CHI_TRA,
      price_difference,
      calculated: true,
      calculatedTimes,
      get_price_from_charge: item.usageLabel === 'Lan 2',
    }
  })

  return result
}

export const calculateChargeDetails = (visitChargeDetails) => {
  let calculatedData = calculateChargeDetailsRaw(visitChargeDetails)

  // it need to run 2 times to make sure all calculated correctly
  calculatedData = calculateChargeDetailsRaw(calculatedData)

  return calculatedData
}

function groupDataVTYT(data) {
  // Nhóm theo mapping_technical_services_id
  // TODO: đổi lại nhóm theo technical_services_cd_id
  const groupedByMapping = data.reduce((acc, item) => {
    const mappingId = item.mapping_technical_services_id || 'undefined'
    if (!acc[mappingId]) {
      acc[mappingId] = []
    }
    acc[mappingId].push(item)
    return acc
  }, {})

  function calculateServiceRate(items) {
    const ssCodeServiceRateMap = {} // Bộ đếm cho từng SS Code (ID_BHYT)
    let globalUsageCount = 0 // Biến đếm số lần xuất hiện chung

    return items.map((item) => {
      // Sử dụng ID_BHYT (SS Code) làm key thay vì MA_NHOM_VAT_TU
      const ssCodeKey = item.ID_BHYT || 'undefined'

      let newServiceRate = 100 // Mặc định là 100

      // Chỉ xử lý khi per_service_flag = true (Flag group 13 = 1)
      if (item.per_service_flag) {
        if (!ssCodeServiceRateMap[ssCodeKey]) {
          // SS Code này chưa xuất hiện trước đó
          globalUsageCount++

          if (globalUsageCount === 1) {
            // Lần đầu tiên: 100%
            newServiceRate = 100
          } else if (globalUsageCount === 2) {
            // Lần 2: 30%
            newServiceRate = 30
          } else if (globalUsageCount === 3) {
            // Lần 3: 30%
            newServiceRate = 30
          } else {
            // Lần 4 trở đi: 10%
            newServiceRate = 10
          }

          // Lưu vào bộ đếm với thông tin về lần xuất hiện
          ssCodeServiceRateMap[ssCodeKey] = {
            usageCount: globalUsageCount,
            serviceRate: newServiceRate,
          }
        } else {
          // SS Code đã tồn tại => Sử dụng service_rate đã tính trước đó
          newServiceRate = ssCodeServiceRateMap[ssCodeKey].serviceRate
        }
      }
      // Nếu per_service_flag = false, giữ nguyên newServiceRate = 100

      return { ...item, service_rate: newServiceRate }
    })
  }

  // Áp dụng tính toán SoLanDung trước khi nhóm theo per_use_flag
  Object.keys(groupedByMapping).forEach((mappingId) => {
    groupedByMapping[mappingId] = calculateServiceRate(groupedByMapping[mappingId])
  })

  // Với mỗi nhóm mapping, nhóm theo per_use_flag
  const result = Object.keys(groupedByMapping).reduce((acc, mappingId) => {
    const items = groupedByMapping[mappingId]
    const perUseGroup = items.reduce((group, item) => {
      // Xét per_use_flag: nếu true thì đặt key là "true", còn lại đặt là "false"
      const key = item.per_use_flag ? 'true' : 'false'
      if (!group[key]) {
        group[key] = []
      }
      group[key].push(item)
      return group
    }, {})

    // Nếu có nhóm per_use_flag = true, nhóm tiếp theo theo MA_NHOM_VAT_TU và đánh dấu số lần dùng
    if (perUseGroup['true']) {
      const maNhomGroup = perUseGroup['true'].reduce((group, item) => {
        const key = item.MA_NHOM_VAT_TU || 'undefined'
        if (!group[key]) {
          group[key] = { count: 0, hasLan1: false, items: [] }
        }
        group[key].count += 1
        let newUsageLabel = ''

        // Gán nhãn số lần dùng dựa vào quantity
        if (item.quantity >= 2) {
          newUsageLabel = `2 Lan`
        } else {
          // Nếu đã có "Lan 1" trước đó, thì đổi thành "Lan 2", nếu chưa có thì đặt là "Lan 1"
          if (!group[key].hasLan1) {
            newUsageLabel = 'Lan 1'
            group[key].hasLan1 = true
          } else {
            newUsageLabel = 'Lan 2'
          }
        }

        group[key].items.push({ ...item, usageLabel: newUsageLabel })
        return group
      }, {})

      // Kiểm tra nếu không có "Lan 1", thì giữ usageLabel là ""
      Object.keys(maNhomGroup).forEach((key) => {
        const items = maNhomGroup[key].items
        const hasLan1 = items.some((item) => item.usageLabel === 'Lan 1')

        if (!hasLan1) {
          items.forEach((item) => {
            if (item.usageLabel !== '2 Lan') {
              item.usageLabel = ''
            }
          })
        }
      })

      perUseGroup['true'] = maNhomGroup
    }

    // Nếu có nhóm per_use_flag = false, gán usageLabel = "" cho tất cả các item
    if (perUseGroup['false']) {
      perUseGroup['false'] = perUseGroup['false'].map((item) => ({
        ...item,
        usageLabel: '',
      }))
    }

    acc[mappingId] = perUseGroup
    return acc
  }, {})

  return result
}

export const getAllXmlData = async (table_1_id, ngayVaoFrom, ngayVaoTo, getDetails = false) => {
  try {
    let filter = []
    if (table_1_id) {
      filter.push(`(table_1_id eq ${table_1_id}) and (include eq true)`)
    }

    const fromDate = ngayVaoFrom
    const toDate = ngayVaoTo || dayjs().format('YYYY-MM-DD')

    if (fromDate) {
      filter.push(`(
        (NGAY_VAO ge ${fromDate} and NGAY_VAO le ${toDate}) or
        (NGAY_RA ne null and NGAY_RA ge ${fromDate} and NGAY_RA le ${toDate})
      )`)
    }
    filter.push(`(include eq true)`)

    // First get all ss_table_1 records
    let ssTable1List = await getItemsService(lists.ss_table_1_nl_view, {
      filter: filter.length > 0 ? filter.join(' and ') : '',
      top: 2000,
    })
    ssTable1List = ssTable1List.value

    if (!ssTable1List || ssTable1List.length === 0) {
      throw new Error('No ss_table_1 data found')
    }

    let table1DetailList = []
    if (getDetails) {
      // handle by parent_id and isParent
      // find detail of table_1_id
      table1DetailList = await Promise.all(
        ssTable1List.map(async (table1Record) => {
          const response = await getItemsService(lists.ss_table_1_nl_view, {
            filter: `parent_id eq ${table1Record.table_1_id}`,
            top: 50,
          })

          if (!response.value || response.value.length === 0) {
            return [table1Record]
          }

          return response.value
        }),
      )

      table1DetailList = table1DetailList.flat()
    }

    // For each ss_table_1 record, get related data from other tables
    const allResults = await Promise.all(
      ssTable1List.map(async (table1Record) => {
        const tables = [
          'ss_table_2_nl_view',
          'ss_table_3_nl_view',
          'ss_table_4_nl_view',
          'ss_table_5_nl_view',
          'ss_table_7_nl_view',
        ]

        const relatedData = await Promise.all(
          tables.map(async (table) => {
            const response = await getItemsService(lists[table], {
              filter: `table_1_id eq ${table1Record.table_1_id}`,
              top: 2000,
            })
            return {
              sheetName: `XML${table.split('_')[2]}`,
              data: response.value || [],
            }
          }),
        )

        let finalTable1Data = [table1Record]
        if (getDetails) {
          finalTable1Data = table1DetailList.filter((d) => d.parent_id === table1Record.table_1_id)
        }

        // Add ss_table_1 record to the results
        return [
          {
            sheetName: 'XML1',
            data: finalTable1Data,
          },
          ...relatedData,
        ]
      }),
    )

    // Merge all results by sheet name
    const mergedResults = allResults.reduce((acc, currentGroup) => {
      currentGroup.forEach(({ sheetName, data }) => {
        if (!acc[sheetName]) {
          acc[sheetName] = { sheetName, data: [] }
        }
        acc[sheetName].data = acc[sheetName].data.concat(data)
      })
      return acc
    }, {})

    return Object.values(mergedResults)
  } catch (error) {
    handleError(error)
  }
}

export const getRegionByCardCode = (cardCode, regions) => {
  return regions.find((item) => item.display_code == cardCode?.substring(3, 5))
}

export const checkValidInvoiceRow = (r) => {
  if (r.extended_item_flag) {
    return true // bypass extended item
  }

  const totalSent = r.lastHistory?.dataSnapshot?.totalSent || r.charge_ss_cover

  // bypass if diff < 1
  const isValidTotal = Math.abs(totalSent - r.ss_cover) < 1

  const isValidManualSSCover = r.children ? true : r.manual_ss_cover_flag

  return isValidTotal && isValidManualSSCover
}

// Hàm chuyển đổi pricing_class thành insuranceType
export const mapInsuranceTypeFromPricingClass = (pricingClassCode) => {
  // Theo nghiệp vụ: "73FA4" tương ứng International, "0A972" tương ứng Local, "UNK" tương ứng Subsidized
  if (pricingClassCode === '73FA4') return 'international'
  else if (pricingClassCode === '0A972') return 'domestic'
  else if (pricingClassCode === 'UNK') return 'unknown' // sử dụng trường hợp không phải international/domestic để về default
  return 'unknown'
}

// Hàm duyệt qua currentHealthInsuranceCardList và tìm policy gợi ý cho mỗi thẻ
export const getSuggestedPoliciesForAllCards = (
  currentPatientVisit,
  currentHealthInsuranceCardList,
  tblSsPolicyShortCodes,
  isTotalLessThanBase15PercentSalary,
  patientPolicySubscriptions = [],
) => {
  // Giả sử pricing_class được lấy từ currentPatientVisitMappingViewDetail.pricing_class
  const pricingClassCode = currentPatientVisit.pricing_class || 'UNK'
  const insuranceType = mapInsuranceTypeFromPricingClass(pricingClassCode)

  // Duyệt từng thẻ bảo hiểm y tế
  const suggestedPolicies = currentHealthInsuranceCardList.map((card) => {
    const patientPolicy = patientPolicySubscriptions.find(
      (item) => item?.card_code == card?.card_code,
    )
    // Giả sử mỗi card có thuộc tính cardType (ví dụ: "CC", "TE", v.v.)
    // Nếu có thông tin về non-copayment thì có thể thay đổi giá trị nonCopayment, ở đây mặc định false
    const nonCopayment = currentPatientVisit.free_copay_flag

    // Tính policy code dựa trên cardType và insuranceType
    const policyCode = getPolicyShortCode(
      card.card_code,
      insuranceType,
      nonCopayment,
      isTotalLessThanBase15PercentSalary,
      card?.referral_disposition_rcd,
    )
    // Từ danh sách tblSsPolicyShortCodes, tìm ra policy có short_code tương ứng
    const suggestedPolicy = tblSsPolicyShortCodes.find((policy) => policy.short_code === policyCode)
    return {
      policy_subscription_id: patientPolicy?.policy_subscription_id,
      card_code: card?.card_code,
      ...suggestedPolicy,
      referral_disposition_rcd: card?.referral_disposition_rcd,
      effective_date: card?.effective_date,
      expiration_date: card?.expiration_date,
    }
  })

  const sortedPolicies = processAndSortPoliciesByCoverage(
    suggestedPolicies.filter((item) => item.short_code),
  )

  return sortedPolicies
}
export const getPolicyShortCode = (
  cardCode,
  insuranceType,
  nonCopayment,
  isTotalLessThanBase15PercentSalary,
  referral_disposition_rcd,
) => {
  // Điều kiện override:
  // Nếu nơi đăng ký khám ban đầu là FV OR tổng tiền BHYT <= 15% lương cơ sở

  const isSubscribe100Percent =
    referral_disposition_rcd === REFERRAL_DISPOSITION.FV_REGISTER_REFERRAL_DISPOTITION_CODE ||
    isTotalLessThanBase15PercentSalary

  if (isSubscribe100Percent) {
    if (insuranceType === 'international') {
      return '859'
    } else if (insuranceType === 'domestic') {
      return '853'
    } else {
      return '847'
    }
  }

  // Định nghĩa các nhóm thẻ theo nghiệp vụ
  const groupBHYT01 = ['1'] // BHYT 01
  const groupBHYT02 = ['2'] // BHYT 02
  const groupBHYT03 = ['3'] // BHYT 03
  const groupBHYT04 = ['4'] // BHYT 04
  const groupBHYT05 = ['5'] // BHYT 05

  let policyCode = null

  // check HT1345 at character 3. => 1 == 1
  if (groupBHYT01.some((code) => cardCode.charAt(2) == code)) {
    // BHYT 01 - 100% cover
    if (insuranceType === 'international') {
      policyCode = '858'
    } else if (insuranceType === 'domestic') {
      policyCode = '852'
    } else {
      policyCode = '846'
    }
  } else if (groupBHYT02.some((code) => cardCode.charAt(2) == code)) {
    // BHYT 02 - 100% cover
    if (insuranceType === 'international') {
      policyCode = '859'
    } else if (insuranceType === 'domestic') {
      policyCode = '853'
    } else {
      policyCode = '847'
    }
  } else if (groupBHYT03.some((code) => cardCode.charAt(2) == code)) {
    // BHYT 03: phân nhánh theo nonCopayment
    if (nonCopayment) {
      // BHYT 03 or 04 - 100% cover with Letter of non-copayment
      if (insuranceType === 'international') {
        policyCode = '863'
      } else if (insuranceType === 'domestic') {
        policyCode = '857'
      } else {
        policyCode = '851'
      }
    } else {
      // BHYT 03 - 95% cover
      if (insuranceType === 'international') {
        policyCode = '860'
      } else if (insuranceType === 'domestic') {
        policyCode = '854'
      } else {
        policyCode = '848'
      }
    }
  } else if (groupBHYT04.some((code) => cardCode.charAt(2) == code)) {
    // BHYT 04: kiểm tra nonCopayment
    if (nonCopayment) {
      // Nếu có tích miễn đồng chi trả → sử dụng bảng BHYT 03 or 04 - 100% cover with Letter of non-copayment
      if (insuranceType === 'international') {
        policyCode = '863'
      } else if (insuranceType === 'domestic') {
        policyCode = '857'
      } else {
        policyCode = '851'
      }
    } else {
      // Nếu không tích → sử dụng BHYT 04 - 80% cover
      if (insuranceType === 'international') {
        policyCode = '861'
      } else if (insuranceType === 'domestic') {
        policyCode = '855'
      } else {
        policyCode = '849'
      }
    }
  } else if (groupBHYT05.some((code) => cardCode.charAt(2) == code)) {
    // BHYT 05 - 100% cover
    if (insuranceType === 'international') {
      policyCode = '862'
    } else if (insuranceType === 'domestic') {
      policyCode = '856'
    } else {
      policyCode = '850'
    }
  } else {
    policyCode = null
  }

  return policyCode
}

// // Ví dụ sử dụng:

// const visit1 = {
//   cardType: 'CC', // thuộc nhóm BHYT 01
//   insuranceType: 'international', // có thẻ bảo hiểm ngoài nước
//   nonCopayment: false
// }

// const visit2 = {
//   cardType: 'HT', // thuộc nhóm BHYT 03
//   insuranceType: 'domestic', // có thẻ bảo hiểm trong nước
//   nonCopayment: true // chọn hưởng miễn đồng chi trả -> dùng bảng BHYT 03 or 04
// }

// const visit3 = {
//   cardType: 'DN', // thuộc nhóm BHYT 04
//   insuranceType: 'none', // chỉ trình BHYT
//   nonCopayment: false
// }

/**
 * Extracts the coverage percentage from a policy name.
 * @param {string} name - The name of the policy (e.g., "100% cover").
 * @returns {number} - The extracted coverage percentage or 0 if not found.
 */
export const extractCoverageFromName = (name) => {
  const match = name.match(/(\d+)%\s*cover/)
  return match ? parseInt(match[1], 10) : 0
}

/**
 * Sorts an array of policies by coverage in descending order.
 * @param {Array} policies - The array of policy objects.
 * @returns {Array} - The sorted array of policies.
 */
export const sortPoliciesByCoverage = (policies) => {
  return policies.sort((a, b) => b.coverage - a.coverage)
}

// Example usage in your existing function
export const processAndSortPoliciesByCoverage = (policies) => {
  try {
    // Add coverage attribute to each policy
    const processedPolicies = policies.map((policy) => ({
      ...policy,
      pricing_class: mapPricingClassFromName(policy?.name_e),
      coverage: extractCoverageFromName(policy?.name_e),
    }))

    // Sort policies by coverage
    return sortPoliciesByCoverage(processedPolicies)
  } catch (error) {
    handleError(error)
    return [] // Trả về mảng rỗng nếu có lỗi
  }
}

export const mapPricingClassFromName = (name) => {
  if (!name) return null

  const lowerCaseName = name.toLowerCase()

  if (lowerCaseName.includes('international insured price')) {
    return '73FA4'
  } else if (lowerCaseName.includes('local insured price')) {
    return '0A972'
  } else if (lowerCaseName.includes('subsidized price')) {
    return 'UNK'
  }

  return null // Trả về null nếu không khớp
}

// Validate KET_LUAN with ss_item_group_rcd === '2'
export const validateKETLUAN = (visitChargeDetails, ui) => {
  const invalidItems = visitChargeDetails.filter(
    (item) => item.ss_item_group_rcd === '2' && !item.KET_LUAN,
  )

  if (invalidItems.length > 0) {
    ui.notiError(
      'Có item chưa có kết luận',
      invalidItems.map((item, index) => (
        <div key={item.visit_charge_detail_id} className="mb-1">
          {index + 1}. Nhóm {item.ss_item_group_rcd}, {item.ss_item_code}:{' '}
          {item.item_name_l || item.item_name_e}
        </div>
      )),
    )

    return false
  }

  return true
}

export const getDisabledVisitDetailPage = ({
  visitDetailMode,
  modeViewData,
  processing_status,
  checkPermission,
} = {}) => {
  const viewOnly = visitDetailMode === FORM_MODE.view
  const isNormalMode = modeViewData === MODE_VIEW_DATA.NORMAL
  const isMSMode = modeViewData === MODE_VIEW_DATA.MS
  const isSentToGateway = processing_status === PROCESSING_STATUS.SENT_TO_GATEWAY.name_e
  const isWaitingBHYT = processing_status === PROCESSING_STATUS.WAITING_BHYT.name_e
  const isSIOManager = checkPermission(PERMISSION.SIO_MANAGER)
  const defaultDisabled = (viewOnly || isSentToGateway) && !isSIOManager
  // custom disabled
  const disabledForSIO = (!isNormalMode || defaultDisabled) && !isSIOManager
  const disabledForMS = (!isMSMode || defaultDisabled || !isWaitingBHYT) && !isSIOManager

  return {
    viewOnly,
    isNormalMode,
    isMSMode,
    isSentToGateway,
    isSIOManager,
    defaultDisabled,

    // custom disabled
    disabledForSIO,
    disabledForMS,
  }
}
