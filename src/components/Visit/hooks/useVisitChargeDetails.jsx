import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { getAllVisitChargeDetailsByPatientVisitId } from '../VisitService'
import { calculateChargeDetails } from '../VisitHelpers'
import nProgress from 'nprogress'
import { updateListItemService } from '../../../common/services'
import lists from '../../../common/lists'
import { MODE_VIEW_DATA } from '../../../common/constant'
import { useDeepCompareMemoize } from 'use-deep-compare-effect'
import { isEqual } from 'lodash'
import { displayDateTime, logDebug } from '../../../common/helpers'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../../store/auth'

// Keys for React Query
export const VISIT_CHARGE_QUERY_KEYS = {
  VISIT_CHARGE_DETAILS: 'visitChargeDetails',
}

export const useVisitChargeDetails = (selectedVisit, enabled = true) => {
  const queryClient = useQueryClient()

  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])

  const visitChargeDetailKey = [
    VISIT_CHARGE_QUERY_KEYS.VISIT_CHARGE_DETAILS,
    selectedVisit?.patient_visit_id,
    modeViewData,
  ]

  const visitChargeDetailsQuery = useQuery({
    queryKey: visitChargeDetailKey,
    queryFn: async () => {
      const data = await getAllVisitChargeDetailsByPatientVisitId(selectedVisit?.patient_visit_id)

      if (!data?.value) {
        return []
      }

      // let visitChargeDetails = data.value // DEBUG: can copy raw from PRD here
      let visitChargeDetails = [
        {
          visit_charge_detail_id: 'e2e92e80-34d9-4e3f-80b3-076d94886397',
          manual_ss_cover_flag: true,
          note: null,
          original_charged_date_time: null,
          original_charged_reason: null,
          updated_user_name: null,
          lu_user_id: '07b0613c-a82d-4275-aac0-08dd6611a57b',
          lu_updated: '2025-06-11T10:53:58.927+07:00',
          charge_detail_id: 'cda412f4-3493-11f0-a2dd-00505691f19a',
          ss_optional_cover_flag: true,
          ss_cover_flag: true,
          patient_visit_id: '8f72e772-17f3-4f51-0f98-08dd96f23571',
          ss_item_code: 'CS/GP-01/07',
          no_ss_item_code: 'CS/GP-01/07',
          item_id: '63496237-1898-452e-816e-ab505c7cb6af',
          ID_BHYT: '02.03.1897',
          item_name_e: 'Consultation General Practice Grade 7 - Doctor Fee',
          item_name_l: 'Khám Nội tổng hợp',
          item_group_name_l: null,
          item_group_seq_num: null,
          top_item_group_name_l: 'Khám Bệnh Ngoại Trú',
          health_insurance_name: 'Khám Nội tổng hợp',
          mapping_technical_services_name: null,
          mapping_technical_services_id: null,
          technical_services_cd_id: null,
          service_requester: '6016010 - FVH Saigon Clinic',
          doctor_name: 'Le Dinh Phuong (Dr.)',
          implementer: 'Le Dinh Phuong (Dr.)',
          unit_price: 416400,
          health_insurance_unit_price: 45000,
          unit: 'Lần',
          quantity: 1,
          total_before_tax: 416400,
          total_after_tax: 416400,
          health_insurance_total_before_tax: null,
          health_insurance_total_after_tax: null,
          purchased_price: null,
          price_difference: -45000,
          policy_coverage_percent: 100,
          policy_name: 'BHYT 02 - 100% cover - Local Insured Price',
          medication_management_fee: 0,
          charged_date_time: '2025-05-19T16:35:15.68+07:00',
          apply_45_months_flag: true,
          per_use_flag: null,
          base_45_months_salary: *********,
          payment_ceiling: null,
          health_insurance_price_with_ceiling: 45000,
          ss_cover_raw: 45000,
          ss_cover: 45000,
          MA_VAT_TU: null,
          MA_NHOM_VAT_TU: null,
          TEN_NHOM_VAT_TU: null,
          ss_item_group_rcd: '13',
          move_up_down_flag: true,
          payment_rate: 100,
          service_rate: 100,
          TYLE_TAI_SD: null,
          per_service_flag: true,
          health_insurance_amount: 45000,
          apply_15_percent_flag: true,
          base_15_percent_salary: null,
          tax: 1,
          health_insurance_unit_price_before_tax: 45000,
          custom_payment_rate: null,
          technical_services_id: 'db02a2ff-9194-4b71-9667-15715473ae41',
          medical_supplies_id: null,
          medicine_id: null,
          serviced_date_time: '2025-05-19T16:35:15.68+07:00',
          purchased_invoice_number: null,
          NGAY_YL: '2025-05-19T16:35:15.68+07:00',
          NGAY_TH_YL: '2025-05-19T16:35:15.68+07:00',
          NGAY_KQ: '2025-05-19T16:35:15.68+07:00',
          BN_CUNG_CHI_TRA: 0,
          BN_TU_CHI_TRA: 371400,
          TT_BN_CHI_TRA: 371400,
          KET_LUAN: null,
          MO_TA: null,
          parent_count: 1,
          GOI_VTYT: null,
          ar_invoice_id: null,
          last_sent_cashier_date_time: '2025-06-11T03:36:02.24+07:00',
          last_sent_cashier_user_id: '9d2d3adb-0e0f-e911-a843-48df37364d48',
          PP_VO_CAM: null,
          charge_detail_lu_updated: '2025-05-19T16:35:15.68+07:00',
          deleted_date_time: null,
          DIEU_KIEN_THANH_TOAN_BHYT: '',
          extended_item_code: null,
          extended_item_flag: false,
          get_price_from_charge: false,
          netoff_flag: false,
          caregiver_employee_id: '398a2517-5846-11d7-a80d-00065bf0ecbd',
          MA_KHOA: 'K03',
        },
        {
          visit_charge_detail_id: 'fd1c3eea-be90-488c-b7de-2f0c1ee882b4',
          manual_ss_cover_flag: true,
          note: null,
          original_charged_date_time: null,
          original_charged_reason: null,
          updated_user_name: null,
          lu_user_id: '07b0613c-a82d-4275-aac0-08dd6611a57b',
          lu_updated: '2025-06-11T10:53:58.927+07:00',
          charge_detail_id: '7de3b084-3494-11f0-a2dd-00505691f19a',
          ss_optional_cover_flag: true,
          ss_cover_flag: true,
          patient_visit_id: '8f72e772-17f3-4f51-0f98-08dd96f23571',
          ss_item_code: 'CS/GYN-01/05',
          no_ss_item_code: 'CS/GYN-01/05',
          item_id: '8611b8d5-0b95-41b6-b579-9ac1710ef490',
          ID_BHYT: '13.27.1897',
          item_name_e: 'Consultation Gynaecology Grade 5 - Doctor Fee',
          item_name_l: 'Khám Phụ sản',
          item_group_name_l: null,
          item_group_seq_num: null,
          top_item_group_name_l: 'Khám Bệnh Ngoại Trú',
          health_insurance_name: 'Khám Phụ sản',
          mapping_technical_services_name: null,
          mapping_technical_services_id: null,
          technical_services_cd_id: null,
          service_requester: '6016010 - FVH Saigon Clinic',
          doctor_name: 'Le Dinh Phuong (Dr.)',
          implementer: 'Le Dinh Phuong (Dr.)',
          unit_price: 386400,
          health_insurance_unit_price: 45000,
          unit: 'Lần',
          quantity: 1,
          total_before_tax: 386400,
          total_after_tax: 386400,
          health_insurance_total_before_tax: null,
          health_insurance_total_after_tax: null,
          purchased_price: null,
          price_difference: -45000,
          policy_coverage_percent: 100,
          policy_name: 'BHYT 02 - 100% cover - Local Insured Price',
          medication_management_fee: 0,
          charged_date_time: '2025-05-19T16:35:14.93+07:00',
          apply_45_months_flag: true,
          per_use_flag: null,
          base_45_months_salary: *********,
          payment_ceiling: null,
          health_insurance_price_with_ceiling: 45000,
          ss_cover_raw: 45000,
          ss_cover: 45000,
          MA_VAT_TU: null,
          MA_NHOM_VAT_TU: null,
          TEN_NHOM_VAT_TU: null,
          ss_item_group_rcd: '13',
          move_up_down_flag: true,
          payment_rate: 100,
          service_rate: 100,
          TYLE_TAI_SD: null,
          per_service_flag: true,
          health_insurance_amount: 45000,
          apply_15_percent_flag: true,
          base_15_percent_salary: null,
          tax: 1,
          health_insurance_unit_price_before_tax: 45000,
          custom_payment_rate: null,
          technical_services_id: '3a994298-0f7f-42e5-89a8-0910b7da2fea',
          medical_supplies_id: null,
          medicine_id: null,
          serviced_date_time: '2025-05-19T16:35:14.93+07:00',
          purchased_invoice_number: null,
          NGAY_YL: '2025-05-19T16:35:14.93+07:00',
          NGAY_TH_YL: '2025-05-19T16:35:14.93+07:00',
          NGAY_KQ: '2025-05-19T16:35:14.93+07:00',
          BN_CUNG_CHI_TRA: 0,
          BN_TU_CHI_TRA: 341400,
          TT_BN_CHI_TRA: 341400,
          KET_LUAN: null,
          MO_TA: null,
          parent_count: 1,
          GOI_VTYT: null,
          ar_invoice_id: null,
          last_sent_cashier_date_time: '2025-06-11T03:36:02.24+07:00',
          last_sent_cashier_user_id: '9d2d3adb-0e0f-e911-a843-48df37364d48',
          PP_VO_CAM: null,
          charge_detail_lu_updated: '2025-05-19T16:35:14.93+07:00',
          deleted_date_time: null,
          DIEU_KIEN_THANH_TOAN_BHYT: '',
          extended_item_code: null,
          extended_item_flag: false,
          get_price_from_charge: false,
          netoff_flag: false,
          caregiver_employee_id: '398a2517-5846-11d7-a80d-00065bf0ecbd',
          MA_KHOA: 'K03',
        },
      ]

      // manifulate charge
      visitChargeDetails = visitChargeDetails.map((item) => ({
        ...item,
        visit_dataset_detail: `${
          selectedVisit?.visit_code
        } - ${displayDateTime(selectedVisit?.actual_visit_datetime)}`,
      }))

      // auth by modeViewData
      if (modeViewData === MODE_VIEW_DATA.CASHIER) {
        visitChargeDetails = visitChargeDetails.filter(
          (item) => item.last_sent_cashier_date_time != null,
        )
      }

      // Calculate charge details
      return calculateChargeDetails(visitChargeDetails)
    },
    enabled: !!selectedVisit?.patient_visit_id && !!modeViewData && enabled,
    refetchInterval: 5 * 60 * 1000,
  })
  const isLoading = visitChargeDetailsQuery.isLoading
  const isSuccess = visitChargeDetailsQuery.isSuccess

  useEffect(
    () => {
      if (
        !selectedVisit?.patient_visit_id ||
        isLoading ||
        (visitChargeDetailsQuery.data?.find((r) => !!r.calculated) && false) ||
        visitChargeDetailsQuery.data?.find((r) => !!r.draft)
      ) {
        return
      }

      const calculatedTimes = visitChargeDetailsQuery.data?.[0]?.calculatedTimes || 0
      const oldData = visitChargeDetailsQuery.data
      const newData =
        visitChargeDetailsQuery.data?.length === 0
          ? []
          : calculateChargeDetails(visitChargeDetailsQuery.data || [])

      const clearCalc = (calcArray) => calcArray.map((r) => ({ ...r, calculatedTimes: 0 }))

      const getPriceOnly = (calcArray) =>
        calcArray.map((r) => ({
          ...r,
          health_insurance_price_with_ceiling: r.health_insurance_price_with_ceiling,
          ss_cover_raw: r.ss_cover_raw,
          ss_cover: r.ss_cover,
          health_insurance_amount: r.health_insurance_amount,
          BN_TU_CHI_TRA: r.BN_TU_CHI_TRA,
          TT_BN_CHI_TRA: r.TT_BN_CHI_TRA,
        }))

      if (!isEqual(clearCalc(oldData), clearCalc(newData))) {
        logDebug('NEED TO CHECK LOOP', getPriceOnly(oldData), getPriceOnly(newData))
        logDebug('calculatedTimes:', calculatedTimes)

        queryClient.setQueryData(visitChargeDetailKey, newData)
      }
    },
    useDeepCompareMemoize([
      selectedVisit?.patient_visit_id,
      visitChargeDetailsQuery.data,
      isLoading,
    ]),
  )

  const moveChargeDetailQuery = useMutation({
    mutationFn: async ({ isMoveUp, visit_charge_detail_id }) => {
      let ssCoverFlag = isMoveUp ? true : false

      const newData = {
        manual_ss_cover_flag: ssCoverFlag,
        last_sent_cashier_date_time: null,
        last_sent_cashier_user_id: null,
      }

      // Optimistically update UI
      queryClient.setQueryData(visitChargeDetailKey, (old) =>
        old?.map((item) =>
          item.visit_charge_detail_id === visit_charge_detail_id ? { ...item, newData } : item,
        ),
      )

      await updateListItemService(lists.visit_charge_detail, visit_charge_detail_id, newData)
    },
    onSuccess: () => {
      refetchData()
    },
  })

  useEffect(() => {
    if (isLoading) {
      nProgress.start()
    } else {
      nProgress.done()
    }

    return () => {
      nProgress.done()
    }
  }, [isLoading])

  // Manually refetch data
  const refetchData = async () => {
    queryClient.invalidateQueries({ queryKey: visitChargeDetailKey })
  }

  return {
    visitChargeDetailsQuery,
    data: visitChargeDetailsQuery.data || [],
    isLoading: isLoading,
    isFetching: visitChargeDetailsQuery.isFetching,
    isSuccess: isSuccess,
    isError: visitChargeDetailsQuery.isError,
    moveChargeDetailQuery,

    refetchData,
  }
}
