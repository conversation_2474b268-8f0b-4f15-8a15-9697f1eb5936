import React from 'react'
import {
  displayCurrency,
  displayDate,
  displayDateTime,
  displayTime,
  handleError,
} from '../../common/helpers'
import { Link } from 'react-router-dom'
import { DANH_SACH_LUOT_KHAM_LINK } from '../../common/constant'
import DVKTCell from './DVKTCell'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCheckSquare } from '@fortawesome/free-solid-svg-icons'
import COLOR from '../../common/color'
import { Button, Popover, Select, Tag } from 'antd'
import PromiseContent from '../../common/components/PromiseContent'
import { getItemService, getItemsService } from '../../common/services'
import lists from '../../common/lists'
import CopyableTag from '../../common/components/CopyableTag'
import { convertBase64ToXml, displayDateTimeFromGate, downloadXMLFile } from '../../SI/helper'
import { checkValidInvoiceRow } from './VisitHelpers'
import { XML_GATE_STATUS } from '../Tool/XmlConstant'
import LazySelect from '../../common/components/LazySelect'

export const REFERRAL_TYPES = {
  FV_REGISTER: 'CB7', // Đăng ký khám chữa bệnh ban đầu tại FV
  RIGHT_CHANNEL_WITH_TRANSFER_CERTIFICATE: 'TRC', // Đúng tuyến có giấy chuyển tuyến
}

export const REFERRAL_DISPOSITION = {
  FV_REGISTER_REFERRAL_DISPOTITION_CODE: 'T0031', // Đăng ký khám chữa bệnh ban đầu tại FV
}

export const POLICY_SUBSCRIPTION_STATUS = {
  NEED_SUBSCRIBE: { name_e: 'NEED_SUBSCRIBE', name_l: 'Cần đăng ký' }, // Cần đăng ký,
  SUBSCRIBED: { name_e: 'SUBSCRIBED', name_l: 'Đã đăng ký' }, // Đã đăng ký
  FAILED: { name_e: 'FAILED', name_l: 'Đăng ký không thành công' }, // Đăng ký không thành công
  WARNING: { name_e: 'WARNING', name_l: 'Cảnh báo' }, // Cảnh báo
}

export const WARNING_STATUS = {
  iconMap: {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌',
    success: '✅',
  },
  title: {
    info: 'Information',
    warning: 'Warning',
    error: 'Error',
    success: 'Success',
  },
}

export const PROCESSING_STATUS = {
  WAITING_FULLFILL_INFO: {
    name_e: 'WAITING_FULLFILL_INFO',
    name_l: 'Chờ điền thông tin BHYT',
  },
  WAITING_BHYT: {
    name_e: 'WAITING_BHYT',
    name_l: 'Chờ nhân viên BHYT xử lý',
  },
  WAITING_CASHIER: {
    name_e: 'WAITING_CASHIER',
    name_l: 'Chờ Cashier xử lý',
  },
  WAITING_MS: {
    name_e: 'WAITING_MS',
    name_l: 'Chờ MS nhập thông tin',
  },
  WAITING_HSBA: {
    name_e: 'WAITING_HSBA',
    name_l: 'Chờ hoàn thành HSBA',
  },
  WAITING_TO_CREATE_XML: {
    name_e: 'WAITING_TO_CREATE_XML',
    name_l: 'MS đã xác nhận và chờ tạo bảng XML',
  },
  WAITING_XML_CREATION: {
    name_e: 'WAITING_XML_CREATION',
    name_l: 'Đang tạo bảng XML',
  },
  WAITING_XML_PROCESSING: {
    name_e: 'WAITING_XML_PROCESSING',
    name_l: 'Chờ xử lý các bảng XML',
  },
  XML_PROCESSED: {
    name_e: 'XML_PROCESSED',
    name_l: 'Đã xử lý các bảng XML',
  },
  SUBMIT_TO_GATE_ERROR: {
    name_e: 'SUBMIT_TO_GATE_ERROR',
    name_l: 'Đẩy cổng lỗi',
  },
  SENT_TO_GATEWAY: {
    name_e: 'SENT_TO_GATEWAY',
    name_l: 'Đã đẩy cổng',
  },
}

export const STATUS_ORDER = [
  'WAITING_BHYT',
  'WAITING_CASHIER',
  'WAITING_MS',
  'WAITING_HSBA',
  'WAITING_TO_CREATE_XML',
  'WAITING_XML_CREATION',
  'WAITING_XML_PROCESSING',
  'XML_PROCESSED',
  'SENT_TO_GATEWAY',
]

export const ACTION_VISIT_HISTORY = {
  CREATE_VISIT: 'CREATE_VISIT',
  SAVE_INFO: 'SAVE_INFO',
  EJECT_MERGED_VISIT: 'EJECT_MERGED_VISIT',
  MERGE_VISIT: 'MERGE_VISIT',
  PROCESS_CHARGE: 'PROCESS_CHARGE',
  MOVE_UP_CHARGE_DETAIL: 'MOVE_UP_CHARGE_DETAIL',
  MOVE_DOWN_CHARGE_DETAIL: 'MOVE_DOWN_CHARGE_DETAIL',
  SEND_TO_CASHIER: 'SEND_TO_CASHIER',
  // CONFIRM
  CASHIER_CONFIRM: 'CASHIER_CONFIRM',
  MS_CONFIRM: 'MS_CONFIRM',
  MS_CONFIRM_HSBA: 'MS_CONFIRM_HSBA',
  SIO_CONFIRM: 'SIO_CONFIRM',

  PRINT_CHARGE: 'PRINT_CHARGE',
  PRINT_INVOICE: 'PRINT_INVOICE',
  PROCESS_INVOICE: 'PROCESS_INVOICE',
  CREATE_XML: 'CREATE_XML',
  CONFIRM_XML: 'CONFIRM_XML',
  SUBMIT_XML: 'SUBMIT_XML',
  SUBSCRIBE_POLICY: 'SUBSCRIBE_POLICY',
  DELETE_XML: 'DELETE_XML',
}

export const ACTION_VISIT_HISTORY_TITLE = {
  CREATE_VISIT: {
    nameE: 'Created visit',
    nameL: 'Tạo lượt khám',
  },
  SAVE_INFO: {
    nameE: 'Saved information',
    nameL: 'Lưu thông tin',
  },
  EJECT_MERGED_VISIT: {
    nameE: 'Ejected merged visit',
    nameL: 'Bỏ lượt khám ra khỏi đợt',
  },
  MERGE_VISIT: {
    nameE: 'Merge visit',
    nameL: 'Merge visit',
  },
  PROCESS_CHARGE: {
    nameE: 'Processed charge',
    nameL: 'Xử lý charge',
  },
  SEND_TO_CASHIER: {
    nameE: 'Send to cashier',
    nameL: 'Gửi cashier',
  },
  CASHIER_CONFIRM: {
    nameE: 'Cashier confirmed',
    nameL: 'Cashier xác nhận',
  },
  MS_CONFIRM: {
    nameE: 'MS confirmed',
    nameL: 'MS đã xác nhận',
  },
  MS_CONFIRM_HSBA: {
    nameE: 'MS confirmed HSBA completion',
    nameL: 'MS xác nhận đang hoàn thành HSBA',
  },
  SIO_CONFIRM: {
    nameE: 'SIO confirmed',
    nameL: 'SIO đã xác nhận',
  },
  PRINT_CHARGE: {
    nameE: 'In bảng kê charge',
    nameL: 'In bảng kê charge',
  },
  PRINT_INVOICE: {
    nameE: 'In bảng kê',
    nameL: 'In bảng kê',
  },
  MOVE_UP_CHARGE_DETAIL: {
    nameE: 'Move up item',
    nameL: 'Move up item',
  },
  MOVE_DOWN_CHARGE_DETAIL: {
    nameE: 'Move down item',
    nameL: 'Move down item',
  },
  PROCESS_INVOICE: {
    nameE: 'Processed invoice',
    nameL: 'Xử lý invoice',
  },
  CREATE_XML: {
    nameE: 'Created XML',
    nameL: 'Tạo bảng XML',
  },
  CONFIRM_XML: {
    nameE: 'Confirm XML',
    nameL: 'Xác nhận đã xử lý XML',
  },
  SUBMIT_XML: {
    nameE: 'Submitted XML',
    nameL: 'Đẩy cổng',
  },
  SUBSCRIBE_POLICY: {
    nameE: 'Subscribed policy',
    nameL: 'Subscribe mức hưởng mới cho bệnh nhân',
  },
  DELETE_XML: {
    nameE: 'Deleted XML',
    nameL: 'Xóa bảng XML',
  },
}

export const SETTING_VISIT_COLUMNS = [
  {
    col_name_display: 'HN',
    table_col_name: 'hn',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Tên bệnh nhân',
    table_col_name: 'patient_name',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Ngày sinh',
    table_col_name: 'date_of_birth',
    datetime_type: true,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Giới tính',
    table_col_name: 'gender',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Mã lượt khám',
    table_col_name: 'visit_id',
    datetime_type: false,
    numeric_type: true,
    col_hidden: false,
  },
  {
    col_name_display: 'Nhóm',
    table_col_name: 'group',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Loại',
    table_col_name: 'type',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Ngày bắt đầu',
    table_col_name: 'start_date',
    datetime_type: true,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Ngày kết thúc',
    table_col_name: 'end_date',
    datetime_type: true,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'Cảnh báo',
    table_col_name: 'warning',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  // {
  //   col_name_display: 'Hành động',
  //   table_col_name: 'action',
  //   datetime_type: false,
  //   numeric_type: false,
  //   col_hidden: false
  // }
]

export const VISIT_ROWS = [
  {
    hn: '*********',
    patient_name: 'Nguyen Van Phung (Mr.)',
    date_of_birth: '05/02/1965',
    gender: 'Male',
    visit_id: '123456789',
    group: 'OPD',
    type: 'OPD-Gastro-Enterology & Hepatology',
    start_date: '29/10/2024 08:28:36',
    end_date: '05/02/2021 08:28:36',
    warning: '⚠️',
    //action: '🔍'
  },
  {
    hn: '*********',
    patient_name: 'Nguyen Van Phung (Mr.)',
    date_of_birth: '05/02/1965',
    gender: 'Male',
    visit_id: '123458789',
    group: 'IPD',
    type: 'OPD-Blood collection',
    start_date: '05/02/2021 08:28:36',
    end_date: '05/02/2021 08:28:36',
    warning: '⚠️',
    //action: '🔍'
  },
  {
    hn: '800311780',
    patient_name: 'Tay Han Tiong (Mr.)',
    date_of_birth: '05/02/1965',
    gender: 'Male',
    visit_id: '258371842',
    group: 'OPD',
    type: 'OPD-Endoscopy',
    start_date: '05/02/2021 08:28:36',
    end_date: '05/02/2021 08:28:36',
    warning: '⛔',
    //action: '🔍'
  },
  {
    hn: '800311783',
    patient_name: 'Vu Thanh Phuong (Mrs.)',
    date_of_birth: '05/02/1965',
    gender: 'Female',
    visit_id: '457525752',
    group: 'OPD',
    type: 'OPD-Blood collection',
    start_date: '05/02/2021 08:28:36',
    end_date: '05/02/2021 08:28:36',
    warning: '⛔',
    //action: '🔍'
  },
  {
    hn: '800311784',
    patient_name: 'Tran Thi Thu Ha (Mrs.)',
    date_of_birth: '05/02/1965',
    gender: 'Female',
    visit_id: '242127345',
    group: 'OPD',
    type: 'OPD-Gastro-Enterology & Hepatology',
    start_date: '05/02/2021 08:28:36',
    end_date: '05/02/2021 08:28:36',
    warning: '✅',
    //action: '🔍'
  },
]

export const TABLE_BHYT_DATA = [
  {
    key: 'FVH-3745323',
    id: 'FVH-3745323',
    children: [
      {
        key: 'LAB3-08',
        id: 'LAB3-08',
        name: 'Creatinine with eGFR (CKD-EPI)',
        doctor: 'Tran Thi Phuong Thao (Dr)',
        costCentreCode: '3041',
        costCentreName: 'Laboratory - General',
        glAccountName: 'OPD - General',
        quantity: 1,
        unitPrice: 140000,
        icon: 'info', // Icon type
      },
      {
        key: 'RT-VMA02',
        id: 'RT-VMA02',
        name: 'Radiotherapy: VMAT; Dose ≤ 2.2 Grays',
        doctor: 'Tran Thi Phuong Thao (Dr)',
        costCentreCode: '2141',
        costCentreName: 'Oncology - General',
        glAccountName: 'Oncology - General',
        quantity: 1,
        unitPrice: 3190000,
        icon: 'warning',
      },
      {
        key: 'TRAN-01/4',
        id: 'TRAN-01/4',
        name: 'Packed Red Blood Cells (250 ml)',
        doctor: 'Tran Thi Phuong Thao (Dr)',
        costCentreCode: '3041',
        costCentreName: 'Laboratory - General',
        glAccountName: 'Laboratory - General',
        quantity: 1,
        unitPrice: 3210000,
        icon: 'info',
      },
    ],
  },
  {
    key: 'FVH-3668833',
    id: 'FVH-3668833',
    children: [
      {
        key: 'CS/ONC-01/08',
        id: 'CS/ONC-01/08',
        name: 'Consultation Oncology Grade 8',
        doctor: 'Tran Thi Phuong Thao (Dr)',
        costCentreCode: '2141',
        costCentreName: 'Oncology - General',
        glAccountName: 'Oncology - General',
        quantity: 1,
        unitPrice: 442000,
        icon: 'error',
      },
    ],
  },
]

export const TABLE_BHYT_COLUMNS = [
  {
    title: 'Mã item',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'Tên item',
    dataIndex: 'name',
    key: 'name',
    render: (text, record) => {
      // Add icons based on the icon type
      const iconMap = {
        info: 'ℹ️',
        warning: '⚠️',
        error: '❌',
      }
      return (
        <>
          {iconMap[record.icon]} {text}
        </>
      )
    },
  },
  {
    title: 'Bác sĩ12',
    dataIndex: 'doctor',
    key: 'doctor',
  },
  {
    title: 'Cost Centre Code',
    dataIndex: 'costCentreCode',
    key: 'costCentreCode',
  },
  {
    title: 'Cost Centre Name',
    dataIndex: 'costCentreName',
    key: 'costCentreName',
  },
  {
    title: 'GL Account Name',
    dataIndex: 'glAccountName',
    key: 'glAccountName',
  },
  {
    title: 'SL',
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: 'Đơn giá',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    render: (price) => {
      if (typeof price === 'number') {
        return price.toLocaleString('vi-VN')
      }
      return price
    },
  },
]

export const PATIENT_INFO = {
  fullname: 'Nguyen Van Phung',
  sex: 'Nam',
  hn: '*********',
  dob: '24/05/1975',
}

export const BHYT_INFO = {
  hn: '*********',
  effective_date: '11/08/2024',
  expiration_date: '10/08/2025',
  type: 'OPD SS w/o Letter of NO Co-payment',
  doi_tuong_kcb: 'SS Right Chanel',
  ma_noi_di: '[79054] Bệnh viện quận Tân Phúc',
  ma_bhyt: '***************',
}
export const TABLE_XML_BHYT_COLUMNS = [
  {
    col_name_display: 'MA_LK',
    table_col_name: 'ma_lk',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'MA_BN',
    table_col_name: 'ma_bn',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'invoice_no',
    table_col_name: 'invoice_no',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'HO VA TEN',
    table_col_name: 'ho_va_ten',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'NGAY SINH',
    table_col_name: 'ngay_sinh',
    datetime_type: true,
    numeric_type: false,
    col_hidden: false,
  },
  {
    col_name_display: 'TRANG THAI',
    table_col_name: 'trang_thai',
    datetime_type: false,
    numeric_type: false,
    col_hidden: false,
  },
]

export const TABLE_XML_BHYT_DATASOURCE = [
  {
    ma_lk: '800785304BT260602138703420470120251600023',
    ma_bn: '*********',
    invoice_no: 'FVH-3671836',
    ho_va_ten: 'Nguyen Van Phung',
    ngay_sinh: '1946-03-20 00:00:00',
    trang_thai: 'Chưa xác nhận xử lý',
  },
  {
    ma_lk: '800785304BT260602138703420470120251647528',
    ma_bn: '*********',
    invoice_no: 'FVH-3671836',
    ho_va_ten: 'Nguyen Van Phung',
    ngay_sinh: '1946-03-20 00:00:00',
    trang_thai: 'Đã xử lý',
  },
]

export const TABLE_HEALTH_INSURANCE_CARD_COLUMNS = [
  {
    title: 'Mã thẻ',
    dataIndex: 'card_code',
    key: 'cardCode',
  },
  {
    title: 'Ngày phát hành',
    dataIndex: 'issue_date',
    key: 'issueDate',
    align: 'right',
    render: (date) => {
      return displayDate(date)
    },
  },
  {
    title: 'Ngày hiệu lực',
    dataIndex: 'effective_date',
    key: 'effectiveDate',
    align: 'right',
    render: (date) => {
      return displayDate(date)
    },
  },
  {
    title: 'Ngày hết hạn',
    dataIndex: 'expiration_date',
    key: 'expiryDate',
    align: 'right',
    render: (date) => {
      return displayDate(date)
    },
  },
  {
    title: 'Nơi đăng ký ban đầu',
    dataIndex: 'referral_disposition_name_l',
    key: 'referralDispositionNameL',
  },
  {
    title: 'Điều kiện hưởng',
    dataIndex: 'coverage_conditions',
    key: 'coverageConditions',
  },
]

export const TABLE_HEALTH_INSURANCE_CARD_DATASOURCE = [
  {
    key: '1',
    card_code: '***************',
    issue_date: '11/08/2024',
    effective_date: '11/08/2024',
    expiration_date: '10/08/2025',
    referral_disposition_name_l: '[79054] Bệnh viện quận Tân Phúc',
    coverage_conditions: '849 - BHYT 04 - 80% COVER - SUBSIDIZED',
    action: null, // This can hold functions or UI components for actions like edit/delete
  },
]

export const TABLE_PATIENT_VISIT_MAPPING_VIEW_COLUMN = [
  {
    title: ' ',
    dataIndex: 'expandControl',
    key: 'expandControl',
    enableFilter: false,
  },
  {
    title: 'Loại',
    dataIndex: 'processing_status',
    key: 'processing_status',
    onCell: (record) => ({ colSpan: record.children ? 2 : 1 }),
    //render: (status) => <b>{PROCESSING_STATUS[status]?.name_l}</b> //Thêm trạng thái vào hàng con
    render: (status, record) => (record.isParent ? <b>{status}</b> : record.visit_type_name_e),
  },

  {
    title: 'HN',
    dataIndex: 'visible_patient_id',
    key: 'visible_patient_id',
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
    render: (text, record) => (record.children ? '' : text),
  },
  {
    title: 'Tên bệnh nhân',
    dataIndex: 'fullname',
    key: 'fullName',
  },
  {
    title: 'Mã lượt khám',
    dataIndex: 'visit_code',
    key: 'visitCode',
  },
  {
    title: 'Ngày bắt đầu lượt khám',
    dataIndex: 'actual_visit_datetime',
    key: 'startDate',
    align: 'right',
    width: 140,
    render: (date, record) => (record.children ? '' : displayDateTime(date)),
  },
  {
    title: 'Trạng thái charge',
    dataIndex: 'process_charge_status',
    key: 'process_charge_status',
    align: 'center',
    render: (text, r) => (r.children ? '' : <Tag>{text}</Tag>),
  },
  {
    title: 'Người đang xử lý',
    dataIndex: 'processing_person_name',
    key: 'processing_person_name',
  },
  {
    title: 'Theo đợt',
    dataIndex: 'treatment_course_flag',
    key: 'treatment_course_flag',
    align: 'center',
    render: (text, r) =>
      text ? (
        <Popover content={'Lượt khám gốc'}>
          <FontAwesomeIcon size="lg" icon={faCheckSquare} color={COLOR.blueLight} />
        </Popover>
      ) : r?.parent_patient_visit_id ? (
        <Popover content={'Lượt khám này đã được merge vào ' + r?.visit_code}>
          <FontAwesomeIcon size="lg" icon={faCheckSquare} color="lightgray" />
        </Popover>
      ) : (
        ''
      ),
  },
  {
    title: 'Ngày kết thúc điều trị',
    dataIndex: 'treatment_course_end_date',
    key: 'treatment_course_end_date',
    align: 'right',
    render: (date, record) => (record.children ? '' : displayDateTime(date)),
  },
  {
    title: 'Ngày kết thúc lượt khám',
    dataIndex: 'closure_visit_datetime',
    key: 'endDate',
    width: 140,
    align: 'right',
    render: (date, record) => (record.children ? '' : displayDateTime(date)),
  },
  {
    title: 'Ngày sinh',
    dataIndex: 'dob',
    key: 'dob',
    align: 'right',
    render: (date, record) => (record.children ? '' : displayDateTime(date)),
  },
  {
    title: 'Giới tính',
    dataIndex: 'sex',
    key: 'gender',
  },
  // {
  //   title: 'Cảnh báo',
  //   dataIndex: 'warning_status',
  //   key: 'warningStatus',
  //   render: (text, record) => (record.children ? '' : WARNING_STATUS.iconMap.info)
  // }
]

// Cấu hình các cột của bảng
export const TABLE_MERGE_VISIT_COLUMNS = [
  {
    title: 'Lượt khám',
    dataIndex: 'visit',
    key: 'visit',
    width: '40%',
  },
  {
    title: 'Loại',
    dataIndex: 'visit_type',
    key: 'visit_type',
    width: '40%',
  },
]

export const getInvoiceCols = (
  medicalTreatmentTypes = [],
  medicalTreatmentInvoices = [],
  setMedicalTreatmentInvoices = () => {},
  setSelectedVisitsInvoiceData = () => {},
  isAdmin = false,
  selectedPatientVisitMappingViews,
) => {
  const cols = [
    {
      title: '',
      width: 0,
      fixed: 'left',
      dataIndex: '',
      key: 'expand',
    },
    {
      title: 'Mã item', // Item code
      width: 80,
      dataIndex: 'item_code',
      key: 'item_code',
    },
    {
      title: 'Tên item', // Item name
      dataIndex: 'invoice_transaction_with_item_name',
      key: 'invoice_transaction_with_item_name',
      fixed: 'left',
      width: 300,
      render: (text, r) => {
        const totalSent = r.lastHistory?.dataSnapshot?.totalSent || 0
        const tagColor = checkValidInvoiceRow(r) ? 'green' : 'red'

        const medicalTreatmentInvoice = medicalTreatmentInvoices?.find(
          (item) => item.invoice_no === r.id,
        )

        // get ss_table_1 by invoice_no
        const getSsTable1 = async () => {
          return await getItemsService(lists.ss_table_1_nl_view, {
            filter: `invoice_no_ eq '${r.id}'`,
            top: 1,
          }).then((res) => res?.value[0])
        }

        if (r.children) {
          const visitOfInvoice = selectedPatientVisitMappingViews?.find(
            (v) => v.patient_visit_id === r.patient_visit_id,
          )

          return (
            <>
              <div className="d-flex justify-content-between align-items-center cursor-pointer">
                <div>
                  <b>{text}</b>
                  <div className="mt-1">
                    <i>{visitOfInvoice?.visit_code}</i>
                    <br />
                    <i>
                      <PromiseContent
                        promise={getSsTable1}
                        render={(res) => (res ? 'Đã tạo XML' : 'Chưa tạo XML')}></PromiseContent>
                    </i>
                  </div>
                </div>
                <div className="w-3/5">
                  <div className="text-end">
                    Sent cashier: <Tag color={tagColor}>{displayCurrency(totalSent, true)}</Tag>
                  </div>
                  <div className="text-end mt-1">
                    Total SS: <Tag color={tagColor}>{displayCurrency(r.ss_cover, true)}</Tag>
                  </div>
                  <div className="text-end mt-1 w-full">
                    <Select
                      optionLabelProp="label"
                      size="small"
                      allowClear
                      className="w-full"
                      placeholder="Chọn loại KCB"
                      onClick={(e) => e.stopPropagation()}
                      defaultValue={medicalTreatmentInvoice?.MA_LOAI_KCB}
                      onChange={(value) => {
                        setMedicalTreatmentInvoices((prev) => {
                          const existingItem = prev.find((item) => item.invoice_no === r.id)
                          if (existingItem) {
                            return prev.map((item) =>
                              item.invoice_no === r.id
                                ? {
                                    ...item,
                                    MA_LOAI_KCB: value,
                                  }
                                : item,
                            )
                          } else {
                            return [
                              ...prev,
                              {
                                invoice_no: r.id,
                                MA_LOAI_KCB: value,
                                active_flag: true,
                              },
                            ]
                          }
                        })
                        setSelectedVisitsInvoiceData((prev) => {
                          return prev.map((item) =>
                            item.id === r.id ? { ...item, MA_LOAI_KCB: value } : item,
                          )
                        })
                      }}>
                      {medicalTreatmentTypes.map((item) => (
                        <Select.Option
                          key={item.MA_LOAI_KCB}
                          value={item.MA_LOAI_KCB}
                          item={item}
                          label={`Loại KCB : ${item.MA_LOAI_KCB} `}>
                          <div className="whitespace-normal">
                            <Tag>{item.MA_LOAI_KCB}</Tag>
                            <div>{item.description}</div>
                          </div>
                        </Select.Option>
                      ))}
                    </Select>
                  </div>
                  <div className="text-end mt-1 w-full">
                    <LazySelect
                      className="w-full"
                      size="small"
                      list={lists.department_mapping_si}
                      keyProp="department_id"
                      orderBy="ten_khoa_bhyt asc"
                      onClick={(e) => e.stopPropagation()}
                      searchFields={['ten_khoa_bhyt', 'ten_khoa_en']}
                      placeholder="Chọn đơn vị"
                      optionLabelProp="label"
                      defaultSelected={
                        medicalTreatmentInvoice?.department_id || visitOfInvoice?.department_id
                          ? {
                              department_id:
                                medicalTreatmentInvoice?.department_id ||
                                visitOfInvoice?.department_id,
                            }
                          : undefined
                      }
                      renderOption={(item) => (
                        <Select.Option
                          key={item.department_id}
                          value={item.department_id}
                          item={item}
                          label={item.ten_khoa_bhyt}>
                          <div className="whitespace-normal">
                            <Tag>Mã FVH: {item.ma_khoa_fv}</Tag>
                            <div>{item.ten_khoa_bhyt}</div>
                          </div>
                        </Select.Option>
                      )}
                      setSelectedUser={(item) => {
                        setMedicalTreatmentInvoices((prev) => {
                          const existingItem = prev.find((i) => i.invoice_no === r.id)
                          if (existingItem) {
                            return prev.map((i) =>
                              i.invoice_no === r.id
                                ? {
                                    ...i,
                                    department_id: item?.department_id,
                                  }
                                : i,
                            )
                          } else {
                            return [
                              ...prev,
                              {
                                invoice_no: r.id,
                                department_id: item?.department_id,
                                active_flag: true,
                              },
                            ]
                          }
                        })
                        setSelectedVisitsInvoiceData((prev) => {
                          return prev.map((item) =>
                            item.id === r.id
                              ? { ...item, department_id: item?.department_id }
                              : item,
                          )
                        })
                      }}
                    />
                  </div>
                </div>
              </div>
            </>
          )
        }

        return (
          <div>
            {text}
            <div>
              {!r.manual_ss_cover_flag && (
                <Tag color="red">Item không nằm trong mục gửi của NV BHYT</Tag>
              )}
            </div>
          </div>
        )
      },
    },
    {
      title: 'Bác sĩ chỉ định', // Doctor name
      width: 57,
      onCell: (record) => ({
        colSpan: record.children ? 0 : 1, // If it's a parent row, this cell is part of the span
      }),
      dataIndex: 'doctor_name',
      key: 'doctor_name',
    },
    {
      title: 'Cost Centre Code', // Cost Centre Code
      width: 57,
      dataIndex: 'cost_centre_code',
      key: 'cost_centre_code',
    },
    {
      title: 'Cost Centre Name', // Cost Centre Name
      width: 57,
      dataIndex: 'cost_centre_name',
      key: 'cost_centre_name',
    },
    {
      title: 'GL Account Name', // GL Account Name
      width: 57,
      dataIndex: 'gl_account_name',
      key: 'gl_account_name',
    },
    {
      title: 'Đơn vị tính', // Unit
      dataIndex: 'unit',
      key: 'unit',
      width: 57,
      align: 'right',
    },
    {
      title: 'SL', // Quantity
      width: 45,
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'right',
      className: 'number',
    },
    {
      title: 'Đơn giá FV', // FV Unit Price
      width: 57,
      dataIndex: 'unit_price',
      key: 'unit_price',
      align: 'right',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'Đơn giá BH', // Health Insurance Unit Price
      width: 57,
      dataIndex: 'health_insurance_price_with_ceiling',
      key: 'health_insurance_price_with_ceiling',
      align: 'right',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'Tổng tiền FV', // total_after_tax
      width: 57,
      dataIndex: 'total_after_tax',
      key: 'total_after_tax',
      align: 'right',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'TL TT DV', // Service Rate
      width: 50,
      dataIndex: 'service_rate',
      key: 'service_rate',
      align: 'right',
      render: (text) => text || '100',
    },
    {
      title: 'TL TT BH', // Insurance Payment Rate
      width: 50,
      dataIndex: 'payment_rate',
      key: 'payment_rate',
      align: 'right',
      render: (text) => text || '100',
    },
    {
      title: 'Tổng tiền BH', // Total Insurance Amount
      width: 57,
      dataIndex: 'health_insurance_amount',
      key: 'health_insurance_amount',
      align: 'right',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'BHYT Thanh toán', // Insurance Payment
      width: 57,
      dataIndex: 'ss_cover',
      key: 'ss_cover',
      align: 'right',
      render: (price, r) => (
        <Popover
          content={
            <div>
              <div>Sent cashier: {displayCurrency(r.charge_ss_cover)}</div>
              <div>Raw: {r.charge_ss_cover}</div>
            </div>
          }>
          <b>{displayCurrency(price)}</b>
        </Popover>
      ),
    },
    {
      title: 'Người bệnh cùng chi trả', // Patient Co-payment
      width: 57,
      dataIndex: 'BN_CUNG_CHI_TRA',
      key: 'BN_CUNG_CHI_TRA',
      align: 'right',
      render: (price) => (price && price > 0 ? displayCurrency(price, true) : null),
    },
    {
      title: 'Khác', // Others
      width: 57,
      dataIndex: 'others',
      key: 'others',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'Người bệnh tự chi trả', // Patient Self Payment (New Column)
      width: 57,
      dataIndex: 'BN_TU_CHI_TRA',
      key: 'BN_TU_CHI_TRA',
      align: 'right',
      render: (price) => displayCurrency(price, true),
    },
    {
      title: 'Ngày y lệnh',
      width: 70,
      dataIndex: 'NGAY_YL',
      key: 'NGAY_YL',
      align: 'right',
      render: (date) => (date ? displayDateTime(date) : null),
    },
  ]

  if (isAdmin) {
    cols.push({
      title: 'admin',
      width: 70,
      key: 'admin',
      align: 'right',
      fixed: 'right',
      render: (_, r) => {
        return r.children ? (
          ''
        ) : (
          <Popover
            trigger="click"
            content={
              <div>
                <div>MO_TA: {r.MO_TA}</div>
                <div>KET_LUAN: {r.KET_LUAN}</div>
              </div>
            }>
            <i className="fa fa-info-circle" />
          </Popover>
        )
      },
    })
  }

  return cols
}

export const TABLE_XML_TABLES_LIST_TAB_COLUMNS = [
  {
    title: 'MA_LK', // Corresponding to MA_LK
    dataIndex: 'MA_LK',
    key: 'MA_LK',
    render: (text, record) => {
      return (
        <div>
          <div>{text}</div>
          <div>
            <PromiseContent
              promise={async () => {
                try {
                  const res = await getItemsService(lists.tbl_file_tracking, {
                    filter: `table_1_id eq ${record?.table_1_id}`,
                  })
                  return res?.value
                } catch (error) {
                  handleError(error, 'TABLE_XML_TABLES_LIST_TAB_COLUMNS')
                }

                return null
              }}
              render={(track = []) => {
                const lastestTrack = track.sort((a, b) => {
                  return new Date(b?.lu_updated) - new Date(a?.lu_updated)
                })?.[0]

                if (!lastestTrack) {
                  return ''
                }

                let thoiGianTiepNhan = displayDateTimeFromGate(lastestTrack?.thoiGianTiepNhan)

                return (
                  <div>
                    <div className="mt-1 fw-bold">Thông tin tiếp nhận từ cổng chính:</div>
                    <div>
                      <div>- Mã giao dịch (tiếp nhận lúc: {thoiGianTiepNhan}):</div>
                      <div>
                        <CopyableTag text={lastestTrack?.maGiaoDich}></CopyableTag>
                      </div>
                      <div className="mt-1">
                        - Thông điệp:{' '}
                        <Tag color={lastestTrack?.maKetQua == '200' ? 'green' : 'red'}>
                          {lastestTrack?.thongDiep}
                        </Tag>
                        <Button
                          size="small"
                          icon={<i className="fa-solid fa-download ms-1"></i>}
                          onClick={() => {
                            const xml_content = convertBase64ToXml(lastestTrack?.file_content)
                            downloadXMLFile(xml_content, lastestTrack?.file_name)
                          }}>
                          Tải xuống file .xml
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              }}></PromiseContent>
          </div>
        </div>
      )
    },
  },

  // {
  //   title: '', // Corresponding to MA_BN
  //   dataIndex: 'warning_status',
  //   key: 'warning_status',
  //   render: (status) => {
  //     return status === null ? WARNING_STATUS.iconMap.warning : WARNING_STATUS.iconMap.info
  //   }
  // },
  {
    title: 'MA_BN', // Corresponding to MA_BN
    dataIndex: 'MA_BN',
    key: 'MA_BN',
  },
  {
    title: 'HỌ VÀ TÊN', // Corresponding to HO_TEN
    dataIndex: 'HO_TEN',
    key: 'HO_TEN',
  },
  {
    title: 'NGÀY SINH', // Corresponding to NGAY_SINH
    dataIndex: 'NGAY_SINH',
    key: 'NGAY_SINH',
    render: (date) => {
      if (date) {
        return new Date(date).toLocaleDateString('vi-VN')
      }
      return ''
    },
  },
  {
    title: 'TRẠNG THÁI', // Corresponding to approve_status or warning_status
    dataIndex: 'approve_status',
    key: 'approve_status',
    render: (status, record) => {
      const statusText =
        APPROVED_STATUS[record?.approve_status]?.text || APPROVED_STATUS.pending.text

      return <span>{statusText}</span>
    },
  },
]

export const APPROVED_STATUS = {
  approved: { icon: '✅', text: 'Đã xử lý' },
  pending: { icon: '⚠️', text: 'Chờ xác nhận xử lý' },
  rejected: { icon: '❌', text: 'Bị từ chối' },
}

export const TABLE_PORTAL_SUBMISSION_COLUMNS = [
  {
    title: 'Mã LK',
    dataIndex: 'MA_LK',
    width: 110,
    key: 'MA_LK',
  },
  {
    title: 'HN',
    dataIndex: 'MA_BN',
    width: 110,
    key: 'MA_BN',
  },
  {
    title: 'Tên bệnh nhân',
    dataIndex: 'HO_TEN',
    width: 110,
    key: 'HO_TEN',
  },
  {
    title: 'Ngày sinh',
    dataIndex: 'NGAY_SINH',
    key: 'NGAY_SINH',
    width: 110,
    align: 'right',
    render: (date) => displayDate(date),
  },
  {
    title: 'Giới tính',
    dataIndex: 'gioi_tinh_',
    width: 70,
    key: 'gioi_tinh_',
  },
  {
    title: 'Ngày vào viện',
    dataIndex: 'NGAY_VAO',
    key: 'NGAY_VAO',
    width: 150,
    render: (date) => displayDateTime(date),
  },
  {
    title: 'Trạng thái đẩy cổng',
    dataIndex: 'gate_status',
    key: 'gate_status',
    width: 110,
    render: (gate_status) => XML_GATE_STATUS[gate_status]?.nameL,
  },
  {
    title: 'Thông tin tiếp nhận',
    key: 'thongDiep',
    width: 170,
    render: (_, r) => (
      <div className="d-flex flex-column gap-1 py-1">
        {/* maKetQua, maGiaoDich, thongDiep, thoiGianTiepNhan */}
        <div>
          <CopyableTag text={r.maGiaoDich}></CopyableTag>
        </div>
        <div>{displayDateTimeFromGate(r.thoiGianTiepNhan)}</div>
        <div>
          <Tag color={r.maKetQua == '200' ? 'green' : 'red'}>{r.thongDiep}</Tag>
        </div>
      </div>
    ),
  },
  // {
  //   title: 'Cảnh báo',
  //   dataIndex: 'warning_status',
  //   key: 'warning_status',
  //   render: (warning_status) => (
  //     <span style={{ color: warning_status === 'warning' ? 'orange' : 'red' }}>
  //       {alert === 'warning' ? '⚠️' : '⚠️'}
  //     </span>
  //   )
  // }
]

export const TABLE_COVERED_CHARGE_SUMMARY_COLUMNS = [
  'total_after_tax', // Total After Tax
  'health_insurance_amount', // Health Insurance Amount
  'ss_cover', // Social Security Cover
  'BN_CUNG_CHI_TRA', // Patient Co-payment
  'BN_TU_CHI_TRA', // Patient Self-payment
  'TT_BN_CHI_TRA', // Total Patient Payment
  // 'health_insurance_total_after_tax' // Health Insurance Total After Tax
]

export const TABLE_COVERED_CHARGE_DETAILS_COLUMNS = [
  {
    title: ' ', // Serial Number
    key: 'stt',
    fixed: 'left',
    width: 0,
    render: (_, r) =>
      r.children ? (
        ''
      ) : (
        <Popover content={r?.error || ''}>
          <div className="position-absolute left-0 top-0 w-100 h-100 d-flex align-items-center justify-content-center">
            <i className="fa fa-info-circle text-light" />
          </div>
        </Popover>
      ),
  },
  {
    title: 'Mã item', // Item Code
    dataIndex: 'ss_item_code',
    fixed: 'left',
    key: 'ss_item_code',
    width: 60,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
    render: (text, r) => (
      <Popover
        content={
          <div>
            <div>
              {r.medical_supplies_id
                ? 'VTYT'
                : r.medicine_id
                  ? 'Thuốc'
                  : r.technical_services_id
                    ? 'DVKT'
                    : 'Chưa mapping Danh mục BHYT cho item này!'}
            </div>
          </div>
        }>
        {text}
      </Popover>
    ),
  },
  {
    title: 'Tên item',
    dataIndex: 'title_group_name',
    key: 'title_group_name',
    width: 270,
    onCell: (record) => ({ colSpan: record.children ? 3 : 1 }),
  },
  {
    title: 'Mã BHYT', // Health Insurance Code
    dataIndex: 'ID_BHYT',
    key: 'ID_BHYT',
    width: 57,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
  },
  {
    title: 'Tên BHYT', // Health Insurance Name
    dataIndex: 'health_insurance_name',
    key: 'health_insurance_name',
    width: 270,
    fixed: 'left',
    render: (text) => text, // Display N/A for null values
  },
  {
    title: 'Tên DVKT', // mapping_technical_services_name
    dataIndex: 'mapping_technical_services_name',
    key: 'mapping_technical_services_name',
    width: 300,
    render: (_, record) => {
      return !record.children && <DVKTCell record={record} />
    },
  },
  {
    title: 'Điều kiện thanh toán BHYT',
    dataIndex: 'DIEU_KIEN_THANH_TOAN_BHYT',
    key: 'DIEU_KIEN_THANH_TOAN_BHYT',
    width: 100,
    flexibleWidth: true,
  },
  {
    title: 'Đơn vị tính', // Unit
    dataIndex: 'unit',
    key: 'unit',
    width: 20,
    align: 'right',
  },
  {
    title: 'SL', // Quantity
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right',
    width: 10,
  },
  {
    title: 'Đơn giá BV', // Unit Price
    dataIndex: 'unit_price',
    width: 57,
    key: 'unit_price',
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Đơn giá BH trước thuế', // Health Insurance Unit Price
    dataIndex: 'health_insurance_unit_price_before_tax',
    key: 'health_insurance_unit_price_before_tax',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Đơn giá BH',
    dataIndex: 'health_insurance_price_with_ceiling',
    key: 'health_insurance_price_with_ceiling',
    width: 55,
    align: 'right',
    render: (price, r) => (
      <Popover
        content={
          <div>
            <div>
              <b>Min: </b>
            </div>
            <div>Trần TT: {r.payment_ceiling}</div>
            <div>ItemSS: {r.health_insurance_unit_price}</div>
            <div>Đơn giá BV: {r.rawUnitPrice}</div>
            <div>Giá mua: {r.purchased_price}</div>
            <div>
              <b>Nhân với: </b>
            </div>
            <div>TYLE_TAI_SD: {r.TYLE_TAI_SD}</div>
          </div>
        }>
        {displayCurrency(price)}
      </Popover>
    ),
  },
  {
    title: 'Trần TT', // payment_ceiling
    dataIndex: 'payment_ceiling',
    width: 55,
    key: 'payment_ceiling',
    align: 'right',
    render: (price) => (price ? displayCurrency(price) : ''),
  },
  {
    title: 'TL TT DV', // Service Rate
    dataIndex: 'service_rate',
    key: 'service_rate',
    width: 50,
    align: 'right',
    render: (text) => text || '100',
  },
  {
    title: 'Thành tiền BV', // Total After Tax
    dataIndex: 'total_after_tax',
    key: 'total_after_tax',
    align: 'right',
    width: 55,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'TL TT BH', // Insurance Payment Rate
    dataIndex: 'payment_rate',
    key: 'payment_rate',
    width: 50,
    align: 'right',
    render: (text) => text || '100',
  },
  {
    title: 'Thành tiền BH', // Health Insurance Total After Tax
    dataIndex: 'health_insurance_amount',
    key: 'health_insurance_amount',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'BHYT chi trả', // Health Insurance Total After Tax
    dataIndex: 'ss_cover',
    key: 'ss_cover',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Người bệnh cùng chi trả', // Health Insurance Total After Tax
    dataIndex: 'BN_CUNG_CHI_TRA',
    key: 'BN_CUNG_CHI_TRA',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Người bệnh tự chi trả', // Health Insurance Total After Tax
    dataIndex: 'BN_TU_CHI_TRA',
    key: 'BN_TU_CHI_TRA',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Tổng tiền người bệnh chi trả', // Health Insurance Total After Tax
    dataIndex: 'TT_BN_CHI_TRA',
    key: 'TT_BN_CHI_TRA',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Giá mua', // Health Insurance Total After Tax
    dataIndex: 'purchased_price',
    key: 'purchased_price',
    align: 'right',
    width: 55,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Số hoá đơn mua', // purchased_invoice_number
    dataIndex: 'purchased_invoice_number',
    key: 'purchased_invoice_number',
    width: 55,
    align: 'right',
  },
  {
    title: 'Giá chênh lệch', // Price Difference
    dataIndex: 'price_difference',
    key: 'price_difference',
    align: 'right',
    width: 45,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'MDM FEE', // Notes
    dataIndex: 'medication_management_fee',
    key: 'medication_management_fee',
    align: 'right',
    width: 45,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Người yêu cầu dịch vụ', // Service Requester
    dataIndex: 'service_requester',
    key: 'service_requester',
    width: 150,
  },
  {
    title: 'Bác sĩ chỉ định', // Doctor
    dataIndex: 'doctor_name',
    width: 150,
    key: 'doctor_name',
  },
  {
    title: 'Ngày y lệnh (Ngày Charge)', // Charged Date
    dataIndex: 'charged_date_time',
    key: 'charged_date_time',
    align: 'right',
    width: 90,
    render: (date) => (date ? displayDateTime(date) : null),
  },
  {
    title: 'Điều kiện hưởng của thẻ',
    dataIndex: 'policy_coverage_percent',
    key: 'policy_coverage_percent',
    align: 'center',
    width: 40,
  },
  {
    title: 'Ghi chú', // Notes
    dataIndex: 'note',
    key: 'note',
    width: 57,
    render: (text) => text || null,
  },
  {
    title: 'Lượt khám',
    dataIndex: 'visit_dataset_detail',
    key: 'visit_dataset_detail',
    width: 180,
    render: (text, r) => <div title={r.patient_visit_id}>{text}</div>,
  },
]

export const TABLE_COVERED_CHARGE_DETAILS_OTMS_COLUMNS = [
  {
    title: ' ', // Serial Number
    key: 'expand',
    fixed: 'left',
    width: 0,
  },
  {
    title: 'Mã item', // Item Code
    dataIndex: 'ss_item_code',
    key: 'ss_item_code',
    width: 115,
    fixed: 'left',
    render: (text, r) =>
      r.children ? '' : `${r.extended_item_code || text}${` [Nhóm ${r.ss_item_group_rcd || ''}]`}`,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
  },
  {
    title: 'Tên item',
    dataIndex: 'title_group_name',
    key: 'title_group_name',
    fixed: 'left',
    width: 200,
    onCell: (record) => ({ colSpan: record.children ? 3 : 1 }),
  },
  {
    title: 'Mã BHYT', // Health Insurance Code
    dataIndex: 'ID_BHYT',
    key: 'ID_BHYT',
    width: 57,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
  },
  {
    title: 'Tên BHYT', // Health Insurance Name
    dataIndex: 'health_insurance_name',
    key: 'health_insurance_name',
    width: 200,
    render: (text) => text, // Display N/A for null values
  },
  {
    title: 'Tên DVKT', // mapping_technical_services_name
    dataIndex: 'mapping_technical_services_name',
    key: 'mapping_technical_services_name',
    width: 200,
    render: (_, record) => {
      return !record.children && <DVKTCell record={record} />
    },
  },
  {
    title: 'Đơn vị tính', // Unit
    dataIndex: 'unit',
    key: 'unit',
    width: 20,
    align: 'right',
  },
  {
    title: 'SL', // Quantity
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right',
    width: 10,
  },
  {
    title: 'Bác sĩ chỉ định', // Doctor
    dataIndex: 'doctor_name',
    width: 120,
    key: 'doctor_name',
  },
  {
    title: 'Ngày y lệnh (Ngày Charge)', // Charged Date
    dataIndex: 'charged_date_time',
    key: 'charged_date_time',
    align: 'right',
    width: 113,
    render: (date) => (date ? displayDateTime(date) : null),
  },
  {
    title: 'Lượt khám',
    dataIndex: 'visit_dataset_detail',
    key: 'visit_dataset_detail',
    width: 223,
  },
  {
    title: 'Ghi chú', // Notes
    dataIndex: 'note',
    key: 'note',
    width: 57,
    render: (text) => text || null,
  },
  {
    title: 'MA_KHOA',
    dataIndex: 'MA_KHOA',
    key: 'MA_KHOA',
    width: 57,
  },
  {
    title: 'Phương pháp vô cảm',
    dataIndex: 'PP_VO_CAM',
    key: 'PP_VO_CAM',
    width: 57,
  },
  {
    title: 'Mô tả',
    dataIndex: 'MO_TA',
    key: 'MO_TA',
    width: 57,
  },
  {
    title: 'Kết luận',
    dataIndex: 'KET_LUAN',
    key: 'KET_LUAN',
    width: 57,
  },
]

export const TABLE_COVERED_CHARGE_DETAILS_CASHIER_COLUMNS = [
  {
    title: ' ', // Serial Number
    key: 'expand',
    fixed: 'left',
    width: 0,
  },
  {
    title: 'Mã item', // Item Code
    dataIndex: 'ss_item_code',
    key: 'ss_item_code',
    fixed: 'left',
    width: 60,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
  },
  {
    title: 'Tên item',
    dataIndex: 'title_group_name',
    key: 'title_group_name',
    fixed: 'left',
    width: 270,
    onCell: (record) => ({ colSpan: record.children ? 3 : 1 }),
    render: (text, r) =>
      r.children ? (
        <b>{text}</b>
      ) : (
        <Popover content={'Last sent cashier: ' + displayDateTime(r.last_sent_cashier_date_time)}>
          <div className={!r.last_sent_cashier_date_time && !r.children ? 'highlight-row-red' : ''}>
            {text}
          </div>
        </Popover>
      ),
  },
  {
    title: 'SL', // Quantity
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right',
    width: 10,
  },
  {
    title: 'Đơn giá BV', // Unit Price
    dataIndex: 'unit_price',
    width: 55,
    key: 'unit_price',
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Thành tiền BV', // Total After Tax
    dataIndex: 'total_after_tax',
    key: 'total_after_tax',
    align: 'right',
    width: 55,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Đơn giá BH',
    dataIndex: 'health_insurance_price_with_ceiling',
    key: 'health_insurance_price_with_ceiling',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Thành tiền BH', // Health Insurance Total After Tax
    dataIndex: 'health_insurance_amount',
    key: 'health_insurance_amount',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'BHYT chi trả', // Health Insurance Total After Tax
    dataIndex: 'ss_cover',
    key: 'ss_cover',
    width: 55,
    align: 'right',
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Người yêu cầu dịch vụ', // Service Requester
    dataIndex: 'service_requester',
    key: 'service_requester',
    width: 220,
  },
  {
    title: 'Bác sĩ chỉ định', // Doctor
    dataIndex: 'doctor_name',
    width: 220,
    key: 'doctor_name',
  },
  {
    title: 'Ghi chú', // Notes
    dataIndex: 'note',
    key: 'note',
    width: 57,
    render: (text) => text || null,
  },
]

export const TABLE_UNCOVERED_CHARGE_DETAILS_COLUMNS = [
  {
    title: ' ', // Serial Number
    // dataIndex: 'key',
    key: 'expan',
    align: 'left',
    fixed: 'left',
    width: 0,
    // render: (text, record, index) => index + 1 // Auto-generate based on index
  },
  {
    title: 'Tên item', // Visit Information
    dataIndex: 'title_group_name',
    key: 'title_group_name',
    fixed: 'left',
    width: 300,
    onCell: (record) => ({ colSpan: record.children ? 2 : 1 }),
  },
  {
    title: 'Mã item', // Item Code
    dataIndex: 'ss_item_code',
    key: 'ss_item_code',
    width: 80,
    onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
  },
  {
    title: 'Tên BHYT',
    dataIndex: 'health_insurance_name',
    key: 'health_insurance_name',
    width: 300,
  },
  {
    title: 'Đơn giá', // Unit Price
    dataIndex: 'unit_price',
    key: 'unit_price',
    align: 'right',
    width: 80,
    render: (price) => displayCurrency(price),
  },
  {
    title: 'Đơn vị tính', // Unit
    dataIndex: 'unit',
    key: 'unit',
    width: 57,
    align: 'right',
    onCell: (r) => ({ width: r.unit ? 'inherit' : 70 }),
  },
  {
    title: 'SL', // Quantity
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right',
    width: 45,
    onCell: (r) => ({ width: r.quantity ? 'inherit' : 70 }),
  },
  {
    title: 'Thành tiền sau thuế', // Total After Tax
    dataIndex: 'total_after_tax',
    key: 'total_after_tax',
    align: 'right',
    width: 80,
    render: (price) => displayCurrency(price),
    onCell: (r) => ({ width: r.total_after_tax ? 'inherit' : 70 }),
  },
  {
    title: 'Người yêu cầu dịch vụ', // Service Requester
    dataIndex: 'service_requester',
    key: 'service_requester',
    width: 80,
    onCell: (r) => ({ width: r.service_requester ? 'inherit' : 70 }),
  },
  {
    title: 'Bác sĩ chỉ định', // Doctor
    dataIndex: 'doctor_name',
    key: 'doctor_name',
    width: 80,
    onCell: (r) => ({ width: r.doctor_name ? 'inherit' : 70 }),
  },
  {
    title: 'Ngày y lệnh (Ngày Charge)', // Charged Date
    dataIndex: 'charged_date_time',
    align: 'right',
    key: 'charged_date_time',
    width: 120,
    render: (date) => (date ? displayDateTime(date) : null),
  },
  {
    title: 'Lượt khám',
    dataIndex: 'visit_dataset_detail',
    key: 'visit_dataset_detail',
    width: 300,
  },
]

// Include '000', '004' for normal validation, 'NOT_FOUND' for cards that couldn't be found on the portal
// Also include special codes for cards with codes 1 and 5
export const VALID_INSURANCE_VALID_CODES = ['000', '004', 'NOT_FOUND']
export const TABLE_MEDICAL_TREATMENT_COLUMNS = [
  {
    title: ' ', // Serial Number
    // dataIndex: 'key',
    key: 'stt',
    align: 'left',
    fixed: 'left',
    width: 30,
    render: (text, record, index) => index + 1, // Auto-generate based on index
  },
  {
    title: 'DIEN BIEN LS',
    dataIndex: 'DIEN_BIEN_LS',
    align: 'left',
    key: 'DIEN_BIEN_LS',
    width: 150,
  },
  {
    title: 'GIAI DOAN BENH',
    dataIndex: 'GIAI_DOAN_BENH',
    align: 'left',
    key: 'GIAI_DOAN_BENH',
    width: 150,
  },
  {
    title: 'HOI CHAN',
    dataIndex: 'HOI_CHAN',
    align: 'left',
    key: 'HOI_CHAN',
    width: 150,
  },
  {
    title: 'PHAU THUAT',
    dataIndex: 'PHAU_THUAT',
    align: 'left',
    key: 'PHAU_THUAT',
    width: 150,
  },
  {
    title: 'THOI DIEM DBLS',
    dataIndex: 'THOI_DIEM_DBLS',
    align: 'left',
    key: 'THOI_DIEM_DBLS',
    width: 150,
    render: (date) => (date ? displayDateTime(date) : null),
  },
  {
    title: 'NGUOI THUC HIEN',
    dataIndex: 'NGUOI_THUC_HIEN',
    align: 'left',
    key: 'NGUOI_THUC_HIEN',
    width: 150,
  },
  {
    title: 'Ngày chỉnh sửa',
    key: 'lu_updated',
    dataIndex: 'lu_updated',
    align: 'right',
    width: 108,
    render: (date) => displayDateTime(date),
  },
  {
    title: 'Chỉnh sửa bởi',
    key: 'lu_user_id',
    dataIndex: 'lu_user_id',
    align: 'right',
    width: 108,
    render: (text) => (
      <PromiseContent
        promise={() =>
          getItemsService(lists.employee_dataset, { filter: `user_id eq ${text}`, top: 1 })
        }
        render={(data) => data?.value?.[0]?.employee_name || 'N/A'}
      />
    ),
  },
]

export const VISIT_SUBSCRIBE_POLICY_HISTORY_SNAPSHOT = {
  [ACTION_VISIT_HISTORY.SUBSCRIBE_POLICY]: {
    makeSnapshot: (policySubscriptionSs, suggestedSubscribePolicyDetail) => {
      return JSON.stringify({ policySubscriptionSs, suggestedSubscribePolicyDetail })
    },
    getSnapshot: (rawSnapshot = '') => {
      if (!rawSnapshot) {
        return ''
      }

      const snapshot = JSON.parse(rawSnapshot)

      return {
        ...snapshot,
        displayE: `Subscribed policy ${snapshot?.policySubscriptionSs?.policyName} for patient`,
        displayL: `Đã subscribe policy ${snapshot?.policySubscriptionSs?.policyName} cho bệnh nhân`,
      }
    },
  },
}
