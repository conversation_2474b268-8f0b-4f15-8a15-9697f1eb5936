import { But<PERSON>, Form, Switch, Row, Col, Tag, Modal, Select } from 'antd'
import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord, MEDICAL_RECORD_KEYS } from '../../queryHooks/useMedicalRecord'
import { displayDate, handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { MODULE_AUTH } from '../../store/auth'
import { handlePrintPDF } from '../../SI/helper'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatient } from '../../queryHooks/usePatient'
import { MEDICAL_RECORD_FORM_TYPE } from './MedicalRecordConstant'
import { useOccupation } from '../../queryHooks/useOccupation'
import { usePatientMedicalCoding } from '../Visit/hooks/usePatientMedicalCoding'
import { useVisitChargeDetails } from '../Visit/hooks/useVisitChargeDetails'
import LazySelect from '../../common/components/LazySelect'

const MedicalOrderForm = ({ mainVisit, selectedMedicalRecordId, patientId }) => {
  const [form] = useForm()
  const app = useApp()
  const queryClient = useQueryClient()

  // hooks
  const { medicalRecordDetail } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const {
    data: { healthInsuranceCards },
  } = usePatientVisit(mainVisit?.patient_visit_id)
  const firstCard = healthInsuranceCards?.[0]
  const { patient } = usePatient({ patientId: patientId || mainVisit?.patient_id })
  const { occupationList } = useOccupation()
  const { patientMedicalCodingList } = usePatientMedicalCoding(mainVisit?.patient_visit_id)
  const { data: visitChargeDetailsData } = useVisitChargeDetails(mainVisit)
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()

  // employee states
  const [openChangeEmployee, setOpenChangeEmployee] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState()

  const isEdit = formMode === FORM_MODE.edit

  const fullName = patient?.Fullname || ''
  const sex = patient?.Sex === 'Male' ? 'Nam' : 'Nữ'
  const dob = displayDate(patient?.DOB, 'DD/MM/YYYY')
  const HN = patient?.HN || ''

  const chargeListOfDoctor = visitChargeDetailsData.filter(
    (r) => r.doctor_name === medicalRecordDetail?.chairperson && !!r.manual_ss_cover_flag,
  )

  // default values
  useDeepCompareEffect(() => {
    form.setFieldsValue({
      diagnosis:
        medicalRecordDetail?.diagnosis ||
        patientMedicalCodingList
          // .filter(
          //   (coding) => coding.coding_employee_id === chargeListOfDoctor[0]?.caregiver_employee_id,
          // )
          .map((coding) => coding.TEN_BENH_CHINH || coding.TEN_BENH_KT)
          .join('; ') ||
        '',
      form_date_time: medicalRecordDetail?.form_date_time
        ? dayjs(medicalRecordDetail.form_date_time)
        : dayjs(),
      chairperson: medicalRecordDetail?.chairperson || '',
    })

    // default employee
    setSelectedEmployee({
      employee_id: medicalRecordDetail?.employee_id,
      employee_name: medicalRecordDetail?.chairperson,
    })
  }, [medicalRecordDetail, patientMedicalCodingList, chargeListOfDoctor])

  const handleSaveMedicalRecordForm = async () => {
    if (!selectedMedicalRecordId) {
      return
    }

    const values = form.getFieldsValue()

    try {
      const newRecord = {
        lu_user_id: currentUser?.User_id,
        diagnosis: values.diagnosis,
        form_date_time: values.form_date_time,
        // employee
        employee_id: selectedEmployee?.employee_id,
        chairperson: selectedEmployee?.employee_name,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      // Invalidate the query cache to force a refresh of the medical record data
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  return (
    <div>
      <div
        className="sticky-top d-flex justify-content-between align-items-center gap-2"
        style={{ top: 105 }}>
        <div>
          <Tag onClick={() => setOpenChangeEmployee(true)} title={medicalRecordDetail?.employee_id}>
            {medicalRecordDetail?.chairperson}
          </Tag>
        </div>
        <div className="d-flex align-items-center gap-2">
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            variant={isEdit ? 'outlined' : 'solid'}
            color={'blue'}
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            type="primary"
            style={{ background: '#2C9538' }}
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>
          <Button
            icon={<i className="fa fa-print" />}
            type="primary"
            style={{ backgroundColor: '#155E75' }}
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(
                `${MEDICAL_RECORD_FORM_TYPE.PHIEU_CHI_DINH.nameE}_${medicalRecordDetail?.title || ''}`,
              )
            }}>
            In phiếu
          </Button>
        </div>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <div id="medical-record-form-print" hidden={viewFileOnly} className="mt-3 px-4">
        <Form form={form} layout="vertical">
          <div className="mb-4">
            <div>
              <img
                src={fvLogoWithText}
                alt="FV Hospital"
                style={{ height: '40px', marginBottom: '10px' }}
                onError={(e) => {
                  e.target.style.display = 'none'
                  e.target.nextSibling.style.display = 'block'
                }}
              />
              <div style={{ display: 'none' }}>FV THOMSON</div>
            </div>
            <h4 className="text-center fw-bold">{MEDICAL_RECORD_FORM_TYPE.PHIEU_CHI_DINH.title}</h4>
          </div>

          {/* Patient Information */}
          <div>Bênh viện FV, {mainVisit.department_ss_name}</div>
          <Row gutter={24}>
            <Col span={14}>
              <div>Họ tên: {fullName}</div>
              <div>Ngày sinh: {dob}</div>
              <div>
                Nghề nghiệp:{' '}
                {mainVisit?.occupation_id
                  ? occupationList.find((o) => o.occupation_id === mainVisit?.occupation_id)
                      ?.name_l || ''
                  : ''}
              </div>
              <div>Địa chỉ: {firstCard?.card_address || ''}</div>
              <div>
                Chẩn đoán:
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName="diagnosis"
                  fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                />
              </div>
            </Col>

            <Col span={10}>
              <div>Mã số bệnh nhân (HN): {HN}</div>
              <div>Giới tính: {sex}</div>
            </Col>
          </Row>

          <div className="mt-3">
            {/* Table charge by doctor_name */}
            <table className="table table-bordered">
              <thead>
                <tr>
                  <th className="text-center">STT</th>
                  <th>Dịch vụ</th>
                  <th className="text-center">Số lượng</th>
                </tr>
              </thead>
              <tbody>
                {chargeListOfDoctor.map((r, index) => (
                  <tr key={index}>
                    <td className="text-center">{index + 1}</td>
                    <td>{r.health_insurance_name || r.item_name_l}</td>
                    <td className="text-center">{r.quantity}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-3">
            <div className="d-flex justify-content-end gap-2 mt-5">
              <div className="d-flex flex-column gap-2 align-items-center">
                <div>
                  {isEdit ? (
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="form_date_time"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.DATETIME}
                    />
                  ) : (
                    ''
                  )}
                  {medicalRecordDetail?.form_date_time
                    ? dayjs(medicalRecordDetail.form_date_time).format(
                        'HH[h]MM [Ngày] DD [tháng] MM [năm] YYYY',
                      )
                    : ''}
                </div>
                <div>Người chỉ định</div>
                <div>
                  <b>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="chairperson"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </b>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </div>

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>

      {openChangeEmployee && (
        <Modal
          title="Chọn bác sĩ chính"
          open={openChangeEmployee}
          footer={false}
          onCancel={() => setOpenChangeEmployee(false)}>
          <div>
            <LazySelect
              className="w-full"
              list={lists.employee_dataset}
              keyProp="employee_id"
              searchFields={['employee_name', 'user_name']}
              placeholder="Chọn bác sĩ chính"
              filter="employee_id ne null"
              defaultSelected={selectedEmployee}
              renderOption={(item) => (
                <Select.Option key={item.employee_id} value={item.employee_id} item={item}>
                  {item.employee_name}
                </Select.Option>
              )}
              setSelectedUser={(item) => {
                setSelectedEmployee(item)
              }}
            />
            <div className="d-flex justify-content-end gap-2 mt-3">
              <AsyncButton
                icon={<i className="fa fa-save" />}
                type="primary"
                onClick={async () => {
                  await handleSaveMedicalRecordForm()
                  setOpenChangeEmployee(false)
                }}>
                Lưu
              </AsyncButton>
            </div>
          </div>
        </Modal>
      )}
    </div>
  )
}

export default MedicalOrderForm
