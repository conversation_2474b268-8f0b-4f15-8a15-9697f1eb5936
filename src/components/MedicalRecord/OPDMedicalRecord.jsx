import { But<PERSON>, <PERSON>, Alert, Switch } from 'antd'
import React, { useState, useEffect } from 'react'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord } from '../../queryHooks/useMedicalRecord'
import { handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { useVisitChargeDetails } from '../Visit/hooks/useVisitChargeDetails'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { visitReasonOptions } from './MedicalRecordConstant'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'
import { handlePrintPDF } from '../../SI/helper'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'

const OPDMedicalRecord = ({ selectedVisit, mainVisit, selectedMedicalRecordId }) => {
  const [form] = useForm()
  const app = useApp()

  // hooks
  const { medicalRecordDetail } = useMedicalRecord({ medicalRecordFormId: selectedMedicalRecordId })
  const { data: visitChargeDetailsData } = useVisitChargeDetails(selectedVisit)
  const { departmentMappings } = useDepartmentMapping()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const isEdit = formMode === FORM_MODE.edit
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()

  const {
    data: { healthInsuranceCards },
  } = usePatientVisit(mainVisit?.patient_visit_id)
  const firstCard = healthInsuranceCards?.[0]

  // Update form values when medical record detail changes
  useDeepCompareEffect(() => {
    if (medicalRecordDetail) {
      form.setFieldsValue({
        visitReason: medicalRecordDetail.visit_reason || '',
        pulse: medicalRecordDetail.pulse || '',
        bloodPressure: medicalRecordDetail.blood_pressure || '',
        temperature: medicalRecordDetail.temperature || '',
        weight: medicalRecordDetail.weight || '',
        height: medicalRecordDetail.height || '',
        bmi: medicalRecordDetail.bmi || '',
        pastMedicalHistory: medicalRecordDetail.past_medical_history || '',
        diseaseProgression: medicalRecordDetail.disease_progression || '',
        clinicalExamination: medicalRecordDetail.clinical_examination || '',
        paraclinicalExamination: medicalRecordDetail.paraclinical_examination || '',
        diagnosis: medicalRecordDetail.diagnosis || '',
        conclusion: medicalRecordDetail.conclusion || '',
        treatment: medicalRecordDetail.treatment || '',
        instructions: medicalRecordDetail.instructions || '',
        departmentId: medicalRecordDetail.department_id || selectedVisit?.department_id || '',
        signedDateTime: medicalRecordDetail.signed_date_time
          ? dayjs(medicalRecordDetail.signed_date_time)
          : dayjs(),
      })
    }
  }, [medicalRecordDetail, selectedVisit])

  // Populate conclusion field with KET_LUAN values from charge records
  useEffect(() => {
    if (visitChargeDetailsData?.length > 0 && isEdit) {
      // Get all KET_LUAN values from charge records
      // Filter for technical services (ss_item_group_rcd == '2') that have KET_LUAN values
      const ketLuanValues = visitChargeDetailsData
        .filter((item) => item.ss_item_group_rcd === '2' && item.KET_LUAN)
        .map((item) => {
          // Format as "Item Name: KET_LUAN value"
          const itemName = item.health_insurance_name || item.item_name_e || item.item_code
          return `${itemName}: ${item.KET_LUAN}`
        })
        .join('\n\n')

      if (ketLuanValues) {
        // Get current conclusion value
        const currentConclusion = form.getFieldValue('conclusion') || ''

        // Only update if the field is empty or if we're in edit mode and the user confirms
        if (!currentConclusion) {
          form.setFieldsValue({
            conclusion: ketLuanValues,
          })
        }
      }
    }
  }, [visitChargeDetailsData, isEdit, form])

  // Update viewFileOnly state when attachments or edit mode changes
  useEffect(() => {
    setViewFileOnly(!!attachments[0] && !isEdit)
  }, [attachments, isEdit])

  const handleSaveMedicalRecordForm = async () => {
    const values = form.getFieldsValue()

    try {
      const newRecord = {
        lu_user_id: currentUser?.User_id,
        visit_reason: values.visitReason,
        pulse: values.pulse,
        blood_pressure: values.bloodPressure,
        temperature: values.temperature,
        weight: values.weight,
        height: values.height,
        bmi: values.bmi,
        past_medical_history: values.pastMedicalHistory,
        disease_progression: values.diseaseProgression,
        clinical_examination: values.clinicalExamination,
        paraclinical_examination: values.paraclinicalExamination,
        diagnosis: values.diagnosis,
        conclusion: values.conclusion,
        treatment: values.treatment,
        instructions: values.instructions,
        department_id: values.departmentId,
        signed_date_time: values.signedDateTime
          ? values.signedDateTime.toISOString()
          : dayjs().toISOString(),
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  // Check if the visit is eligible for an OPD medical record using mainVisit
  const isEligible = !(
    mainVisit?.treatment_course_flag === false || mainVisit?.visit_type_group_rcd === 'IPD'
  )

  return (
    <div>
      {!isEligible && (
        <Alert
          message="Lượt khám không đủ điều kiện"
          description="Lượt khám này không đủ điều kiện để tạo Bệnh án ngoại trú (treatment_course_flag = false hoặc visit_type_group_rcd = IPD)"
          type="warning"
          showIcon
          className="mb-3"
        />
      )}

      <div
        className="sticky-top d-flex justify-content-end align-items-center gap-2 pb-2"
        style={{ top: 105 }}>
        {/* toggle to view file or form */}
        <div className="d-flex align-items-center me-2 gap-2">
          <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ xem
          File đính kèm
        </div>
        <Button
          variant={isEdit ? 'outlined' : 'solid'}
          color="blue"
          icon={<i className="fa fa-edit" />}
          onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}
          disabled={!isEligible}>
          {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
        </Button>
        <AsyncButton
          icon={<i className="fa fa-save" />}
          hidden={!isEdit}
          disabled={!isEligible}
          onClick={handleSaveMedicalRecordForm}>
          Lưu
        </AsyncButton>
        <Button
          type="primary"
          style={{ background: '#2C9538' }}
          icon={openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />}
          onClick={() => setOpenDocumentStore(!openDocumentStore)}>
          {openDocumentStore ? 'Đóng' : 'Mở'} upload
        </Button>
        <Button
          icon={<i className="fa fa-print" />}
          type="primary"
          style={{ backgroundColor: '#155E75' }}
          onClick={() => {
            setViewFileOnly(false)
            setOpenDocumentStore(false)
            setFormMode(FORM_MODE.view)

            handlePrintPDF(`BenhAnNgoaiTru_${medicalRecordDetail?.title || ''}`)
          }}>
          In phiếu
        </Button>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <div id="medical-record-form-print" hidden={viewFileOnly} className="mt-3">
        <Form form={form} layout="vertical">
          <div>
            <img
              src={fvLogoWithText}
              alt="FV Hospital"
              style={{ height: '40px', marginBottom: '10px' }}
            />
            <div style={{ display: 'none' }}>FV THOMSON</div>
          </div>

          <div className="mb-4 ps-4">
            <h4 className="text-center fw-bold">BỆNH ÁN NGOẠI TRÚ CHUNG</h4>

            {/* Patient Info */}
            <div className="row mb-4">
              <div className="d-flex align-items-center">
                <span>Bệnh viện FV, Khoa: </span>
                <div className="ms-2" style={{ minWidth: '200px' }}>
                  <MedicalRecordField
                    form={form}
                    formMode={formMode}
                    fieldName="departmentId"
                    fieldType={MEDICAL_RECORD_FIELD_TYPE.SELECT}
                    selectOptions={departmentMappings.map((dept) => ({
                      value: dept.department_id,
                      label: dept.ten_khoa_vn || dept.ten_khoa_en,
                    }))}
                  />
                </div>
              </div>
              <div className="col-8">
                <div>Họ tên: {mainVisit?.fullname || ''}</div>
                <div>
                  Ngày sinh: {mainVisit?.dob ? dayjs(mainVisit.dob).format('DD/MM/YYYY') : ''}
                </div>
                <div>Nghề nghiệp: Khác</div>
                <div>Địa chỉ: {firstCard?.card_address || ''}</div>
              </div>
              <div className="col-4">
                <div>HN: {mainVisit?.visible_patient_id || ''}</div>
                <div>Giới tính: {mainVisit?.sex === 'Male' ? 'Nam' : 'Nữ'}</div>
              </div>
            </div>

            {/* I. Lý do khám bệnh */}
            <div className="mb-3">
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="visitReason"
                label="I. Lý do khám bệnh:"
                templateOptions={visitReasonOptions}
              />
            </div>

            {/* II. Tổng trạng */}
            <div className="mb-3">
              <div className="fw-bold mb-2">II. Tổng trạng</div>
              <ul className="list-unstyled">
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• Mạch:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="pulse"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• Huyết áp:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="bloodPressure"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• Nhiệt độ:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="temperature"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• Cân nặng:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="weight"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• Chiều cao:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="height"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
                <li className="mb-2">
                  <div className="d-flex align-items-center">
                    <span className="me-2 w-[120px]">• BMI:</span>
                    <MedicalRecordField
                      form={form}
                      formMode={formMode}
                      fieldName="bmi"
                      fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXT}
                    />
                  </div>
                </li>
              </ul>
            </div>

            {/* III. Tiền sử bệnh */}
            <div className="mb-3">
              <div className="fw-bold mb-2">III. Tiền sử bệnh</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="pastMedicalHistory"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* IV. Quá trình bệnh lý */}
            <div className="mb-3">
              <div className="fw-bold mb-2">IV. Quá trình bệnh lý</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="diseaseProgression"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* V. Khám lâm sàng */}
            <div className="mb-3">
              <div className="fw-bold mb-2">V. Khám lâm sàng</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="clinicalExamination"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* VI. Cận lâm sàng */}
            <div className="mb-3">
              <div className="fw-bold mb-2">VI. Cận lâm sàng</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="paraclinicalExamination"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* VII. Chẩn đoán */}
            <div className="mb-3">
              <div className="fw-bold mb-2">VII. Chẩn đoán</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="diagnosis"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* VIII. Kết luận */}
            <div className="mb-3">
              <div className="fw-bold mb-2">VIII. Kết luận</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="conclusion"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* IX. Điều trị */}
            <div className="mb-3">
              <div className="fw-bold mb-2">IX. Điều trị</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="treatment"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* X. Lời dặn/Hướng dẫn */}
            <div className="mb-3">
              <div className="fw-bold mb-2">X. Lời dặn/Hướng dẫn</div>
              <MedicalRecordField
                form={form}
                formMode={formMode}
                fieldName="instructions"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.TEXTAREA}
              />
            </div>

            {/* Signature */}
            <div className="mt-4">
              <div>
                <span className="me-2">Ngày ký:</span>
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName="signedDateTime"
                  fieldType={MEDICAL_RECORD_FIELD_TYPE.DATE}
                />
              </div>
            </div>
          </div>
        </Form>
      </div>

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>
    </div>
  )
}

export default OPDMedicalRecord
