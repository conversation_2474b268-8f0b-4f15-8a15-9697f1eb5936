// Example usage of usePopulatePDF hook
import React, { useState } from 'react'
import { Button, Upload, Table, Input, Checkbox } from 'antd'
import { usePopulatePDF } from './usePopulatePDF'

const PDFFormExample = () => {
  const [pdfFile, setPdfFile] = useState(null)
  const { fieldList, setFieldList, populateAndExportPDF } = usePopulatePDF(pdfFile)
  const [fieldValues, setFieldValues] = useState({})

  // Handle PDF file upload
  const handleFileUpload = (file) => {
    setPdfFile(file)
    return false // Prevent automatic upload
  }

  // Handle field value changes
  const handleFieldChange = (fieldName, value) => {
    setFieldValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }))
  }

  // Handle form submission and PDF export
  const handleExportPDF = async () => {
    try {
      await populateAndExportPDF(fieldValues, 'completed-form.pdf')
    } catch (error) {
      console.error('Error exporting PDF:', error)
    }
  }

  // Table columns for displaying form fields
  const columns = [
    {
      title: 'Field Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Current Value',
      dataIndex: 'value',
      key: 'value',
      render: (value, record) => {
        if (record.type === 'checkbox') {
          return value ? 'Checked' : 'Unchecked'
        }
        return String(value)
      },
    },
    {
      title: 'New Value',
      key: 'newValue',
      render: (_, record) => {
        if (record.type === 'text') {
          return (
            <Input
              placeholder="Enter value"
              value={fieldValues[record.name] || ''}
              onChange={(e) => handleFieldChange(record.name, e.target.value)}
            />
          )
        } else if (record.type === 'checkbox') {
          return (
            <Checkbox
              checked={fieldValues[record.name] || false}
              onChange={(e) => handleFieldChange(record.name, e.target.checked)}
            />
          )
        } else if (record.type === 'radio' || record.type === 'dropdown') {
          return (
            <select
              value={fieldValues[record.name] || ''}
              onChange={(e) => handleFieldChange(record.name, e.target.value)}>
              <option value="">Select option</option>
              {record.options.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          )
        }
        return 'Unsupported field type'
      },
    },
  ]

  return (
    <div style={{ padding: '20px' }}>
      <h2>PDF Form Populator Example</h2>

      {/* File Upload */}
      <div style={{ marginBottom: '20px' }}>
        <Upload accept=".pdf" beforeUpload={handleFileUpload} showUploadList={false}>
          <Button>Upload PDF Form</Button>
        </Upload>
        {pdfFile && <p>Selected file: {pdfFile.name}</p>}
      </div>

      {/* Form Fields Table */}
      {fieldList.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Form Fields ({fieldList.length} fields found)</h3>
          <Table
            dataSource={fieldList}
            columns={columns}
            rowKey="name"
            pagination={false}
            size="small"
          />
        </div>
      )}

      {/* Export Button */}
      {fieldList.length > 0 && (
        <Button type="primary" onClick={handleExportPDF} disabled={!pdfFile}>
          Populate and Download PDF
        </Button>
      )}

      {/* Alternative usage examples */}
      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f5f5f5' }}>
        <h3>Alternative Usage Examples:</h3>
        <pre>{`
// Example 1: Using object mapping
const fieldData = {
  'firstName': 'John',
  'lastName': 'Doe',
  'email': '<EMAIL>',
  'agreeToTerms': true
}
await populateAndExportPDF(fieldData, 'user-form.pdf')

// Example 2: Using array format
const fieldArray = [
  { name: 'firstName', value: 'John' },
  { name: 'lastName', value: 'Doe' },
  { name: 'agreeToTerms', value: true }
]
await populateAndExportPDF(fieldArray)

// Example 3: Programmatically update field list
setFieldList(prevFields => 
  prevFields.map(field => 
    field.name === 'specificField' 
      ? { ...field, value: 'newValue' }
      : field
  )
)
        `}</pre>
      </div>
    </div>
  )
}

export default PDFFormExample
