import React from 'react'
import Compressor from 'compressorjs'
import _, { isNumber } from 'lodash'
import moment from 'moment'
import dayjs from './dayjs'
import { utils, writeFile } from 'xlsx'
import {
  faFile,
  faFileExcel,
  faFilePdf,
  faFilePowerpoint,
  faFileWord,
  faImage,
} from '@fortawesome/free-solid-svg-icons'
import { SearchOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import { getItemsService } from './services'

export function handleError(error, from) {
  let message = ''

  // for fetching
  if (error.response) {
    message = error.response.data

    if (message['odata.error']?.code?.indexOf('SPDuplicateValuesFoundException') > -1) {
      message = 'Duplicate Value'
    }

    if (message['odata.error']?.message?.value) {
      message = message['odata.error']?.message?.value
    }

    if (error.response?.status === 401) {
      message = '401'
    }
  } else {
    // for system
    message = error.message
  }

  message = JSON.stringify(message)
  if (message.indexOf('指定した名前は既に使用されています') > -1) {
    message = 'Tên file đã tồn tại'
  }

  if (message.indexOf('The specified name is already in use.') > -1) {
    message = 'Tên file đã tồn tại'
  }
  console.error(`[handleError/${from}]`, message)

  return message
}

export const filterOption = (input, option = {}, ignoreValue = 'Other') => {
  try {
    if (option.key === ignoreValue) {
      return true
    }

    return (
      removeVietnameseTones(option.label ? option.label : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1 ||
      removeVietnameseTones(option.value ? option.value : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1 ||
      removeVietnameseTones(option.key ? option.key : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1 ||
      removeVietnameseTones(option.filter1 ? option.filter1 : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1 ||
      removeVietnameseTones(option.filter2 ? option.filter2 : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1 ||
      removeVietnameseTones(option.filter3 ? option.filter3 : '')
        .toString()
        .toLocaleLowerCase()
        .indexOf(removeVietnameseTones(input).toLocaleLowerCase()) > -1
    )
  } catch (error) {
    return false
  }
}

export function hexToBase64(str) {
  return btoa(
    String.fromCharCode.apply(
      null,
      str
        .replace(/\r|\n/g, '')
        .replace(/([\da-fA-F]{2}) ?/g, '0x$1 ')
        .replace(/ +$/, '')
        .split(' '),
    ),
  )
}

export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result?.toString() || '')
    reader.onerror = (error) => reject(error)
  })
}

export function fileToBinary(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (evt) => {
      const bstr = evt.target.result
      resolve(bstr)
    }

    reader.onerror = () => reject('Fail')

    reader.readAsBinaryString(file)
  })
}

export function getNormalizedFile(file, quality = 0.5, max = 500) {
  return new Promise((resolve, reject) => {
    new Compressor(file, {
      maxWidth: max,
      maxHeight: max,
      success(normalizedFile) {
        resolve(normalizedFile)
      },
      error(error) {
        reject(error)
      },
      quality: quality,
    })
  })
}

export function fileToBlob(file) {
  return new Promise((resolve) => {
    var reader = new FileReader()
    reader.onload = function () {
      var blob = window.dataURLtoBlob(reader.result)

      resolve(blob)
    }
    reader.readAsDataURL(file)
  })
}

export function isImage(file) {
  const ext = file.name.split('.').pop().toLocaleLowerCase()

  return ['jpg', 'jpeg', 'png', 'gif', 'heic'].includes(ext)
}

export function updateItem(arr, item, update) {
  const tmpArr = [...arr]
  const index = tmpArr.findIndex((a) => _.isEqual(a, item))

  if (index === -1) {
    return tmpArr
  }

  if (!update) {
    tmpArr[index] = { ...item }
  } else {
    tmpArr[index] = { ...item, ...update }
  }

  return tmpArr
}

export function patchItem(arr, item, update = null) {
  const tmpArr = [...arr]
  const index = tmpArr.findIndex((a) => _.isEqual(a, item))

  if (index === -1) {
    return [...tmpArr, item]
  }

  if (!update) {
    tmpArr[index] = { ...item }
  } else {
    tmpArr[index] = { ...item, ...update }
  }

  return tmpArr
}

export function displayValueByTypeRcd(value, typeRcd) {
  // Switch statement for handling different types
  switch (typeRcd) {
    case 'TimePicker':
      return displayTime(value) || null

    case 'DatePicker':
      return displayDate(value)

    case 'DateTimePicker':
      return displayDateTime(value)
    case 'MonthPicker':
      return displayMonthTime(value)

    default:
      return value // Default case to return new_value_e or 'n/a' if new_value_e is empty
  }
}
export function displayDate(date, format = 'YYYY-MM-DD') {
  //test
  if (!date) {
    return 'n/a'
  }

  // case dayjs
  if (date instanceof dayjs) {
    return date.format('YYYY-MM-DD HH:mm')
  }

  const newDate = moment(date)

  if (!newDate.isValid()) {
    return <span className="text-danger">Invalid date</span>
  }

  return newDate.format(format)
}

export function displayDateTime(date, format = 'YYYY-MM-DD HH:mm') {
  if (!date) {
    return 'n/a'
  }

  // case dayjs
  if (date instanceof dayjs) {
    return date.format(format)
  }

  return moment(date).format(format)
}
export function displayMonthTime(date) {
  if (!date) {
    return 'n/a'
  }

  return moment(date).format('MMM-YYYY')
}
export function displayTime(date) {
  if (!date) {
    return 'n/a'
  }

  return moment(date).format('HH:mm A')
}

/**
 *
 * @param {DatePicker} date from datepicker
 */
export function getStartOfDate(date) {
  return moment(date.toDate().setHours(0, 0, 0)).toISOString()
}

/**
 *
 * @param {DatePicker} date from datepicker
 */
export function getEndOfDate(date) {
  return moment(date.toDate().setHours(23, 59, 59)).toISOString()
}

export async function batchPromises(arrPromises = [], initSize = 20) {
  const size = initSize
  const batchSize = Math.ceil(arrPromises.length / size)

  var batches = []
  var batchResults = []

  for (let i = 0; i < batchSize; i++) {
    batches[i] = arrPromises.slice(i * size, (i + 1) * size)
  }

  for (let i = 0; i < batches.length; i++) {
    const items = batches[i]

    const results = await Promise.all(items.map((p) => p()))

    batchResults = [...batchResults, ...results]
  }

  return batchResults
}

export const ConvertUTCToLocalHourAndMinuteTime = (UTCTime) => {
  const utcTime = UTCTime
  const utcDateTime = new Date(utcTime)
  const localHours = utcDateTime.getHours()
  const localMinutes = utcDateTime.getMinutes()
  const localTime = `${String(localHours).padStart(2, '0')}:${String(localMinutes).padStart(
    2,
    '0',
  )}`
  return localTime
}
export const getTimeValue = (DateTime) => {
  const timestamp = DateTime

  const date = new Date(timestamp)
  const hours = String(date.getHours()).padStart(2, '0') // 22 (pad with leading zero if necessary)
  const minutes = String(date.getMinutes()).padStart(2, '0') // 41 (pad with leading zero if necessary)

  const formattedTime = `${hours}:${minutes}`

  return formattedTime
}

export const getDateValue = (DateTime) => {
  const timestamp = DateTime

  const date = new Date(timestamp)
  const year = date.getFullYear() // 2023
  const month = String(date.getMonth() + 1).padStart(2, '0') // 06 (months are zero-based, so we add 1 and pad with leading zeros if necessary)
  const day = String(date.getDate()).padStart(2, '0') // 25 (pad with leading zeros if necessary)

  const formattedDate = `${year}-${month}-${day}`

  return formattedDate
}

export const convertDateTimeToDatejs = (date, time) => {
  if (date == null || time == null) {
    return null
  } else {
    var dayjsObj = dayjs(moment(date + time, 'YYYY-MM-DDLT'))

    return dayjsObj
  }
}

export const exportToExcel = (
  dataToExport,
  fileName,
  option = {
    sheetName: 'Data',
  },
) => {
  /* generate worksheet from state */
  const ws = utils.json_to_sheet(dataToExport)

  /* create workbook and append worksheet */
  const wb = utils.book_new()

  utils.book_append_sheet(wb, ws, option.sheetName)

  /* export to XLSX */
  writeFile(wb, fileName)
}

export const excelDateToMoment = (excelDateNumber) => {
  return moment(new Date((excelDateNumber - 25569) * 86400000))
}

// Sheet Verion at A1
export const getVersionExcelTemplate = (wb) => {
  try {
    const ws = wb.Sheets['Version']
    const versionText = ws['A1'].v

    return versionText
  } catch (error) {
    return 'notfound'
  }
}

export function base64toFile(base64String, filename, mimeType, needToSplit = true) {
  // Split the base64 string to get the content type and data
  const base64Data = needToSplit ? base64String.split(',')[1] : base64String

  // Convert the base64 data to a binary array
  const binaryData = atob(base64Data)

  // Create a Uint8Array from the binary data
  const uint8Array = new Uint8Array(binaryData.length)
  for (let i = 0; i < binaryData.length; i++) {
    uint8Array[i] = binaryData.charCodeAt(i)
  }

  // Create a Blob from the Uint8Array
  const blob = new Blob([uint8Array], { type: mimeType })

  // Create a File from the Blob
  const file = new File([blob], filename, { type: mimeType })

  return file
}

export function getWLMappingID(src, WorkLocationID) {
  return src.find((w) => w.WorkLocationID === WorkLocationID)?.MappingID
}

export const downloadBase64File = (fileName, base64) => {
  let downloadName = fileName.replace(/\.[^/.]+$/, '')
  downloadName = `${downloadName}.pdf`

  const a = document.createElement('a')
  a.href = base64
  a.download = downloadName

  document.body.appendChild(a)
  a.click()

  document.body.removeChild(a)
}

export const customEncodeURIComponent = (str) => {
  return encodeURIComponent(str)
    .replace(/[!'()*]/g, function (c) {
      return '%' + c.charCodeAt(0).toString(16)
    })
    .replace(/%20/g, '%20')
}

export const tiengVietKhongDau = (input) => {
  const dic = [
    { key: 'ạ', value: 'a' },
    { key: 'ả', value: 'a' },
    { key: 'ã', value: 'a' },
    { key: 'à', value: 'a' },
    { key: 'á', value: 'a' },
    { key: 'â', value: 'a' },
    { key: 'ậ', value: 'a' },
    { key: 'ầ', value: 'a' },
    { key: 'ấ', value: 'a' },
    { key: 'ẩ', value: 'a' },
    { key: 'ẫ', value: 'a' },
    { key: 'ă', value: 'a' },
    { key: 'ắ', value: 'a' },
    { key: 'ằ', value: 'a' },
    { key: 'ặ', value: 'a' },
    { key: 'ẳ', value: 'a' },
    { key: 'ẵ', value: 'a' },
    { key: 'ó', value: 'o' },
    { key: 'ò', value: 'o' },
    { key: 'ọ', value: 'o' },
    { key: 'õ', value: 'o' },
    { key: 'ỏ', value: 'o' },
    { key: 'ô', value: 'o' },
    { key: 'ộ', value: 'o' },
    { key: 'ổ', value: 'o' },
    { key: 'ỗ', value: 'o' },
    { key: 'ồ', value: 'o' },
    { key: 'ố', value: 'o' },
    { key: 'ơ', value: 'o' },
    { key: 'ờ', value: 'o' },
    { key: 'ớ', value: 'o' },
    { key: 'ợ', value: 'o' },
    { key: 'ở', value: 'o' },
    { key: 'ỡ', value: 'o' },
    { key: 'é', value: 'e' },
    { key: 'è', value: 'e' },
    { key: 'ẻ', value: 'e' },
    { key: 'ẹ', value: 'e' },
    { key: 'ẽ', value: 'e' },
    { key: 'ê', value: 'e' },
    { key: 'ế', value: 'e' },
    { key: 'ề', value: 'e' },
    { key: 'ệ', value: 'e' },
    { key: 'ể', value: 'e' },
    { key: 'ễ', value: 'e' },
    { key: 'ú', value: 'u' },
    { key: 'ù', value: 'u' },
    { key: 'ụ', value: 'u' },
    { key: 'ủ', value: 'u' },
    { key: 'ũ', value: 'u' },
    { key: 'ư', value: 'u' },
    { key: 'ự', value: 'u' },
    { key: 'ữ', value: 'u' },
    { key: 'ử', value: 'u' },
    { key: 'ừ', value: 'u' },
    { key: 'ứ', value: 'u' },
    { key: 'í', value: 'i' },
    { key: 'ì', value: 'i' },
    { key: 'ị', value: 'i' },
    { key: 'ỉ', value: 'i' },
    { key: 'ĩ', value: 'i' },
    { key: 'ý', value: 'y' },
    { key: 'ỳ', value: 'y' },
    { key: 'ỷ', value: 'y' },
    { key: 'ỵ', value: 'y' },
    { key: 'ỹ', value: 'y' },
    { key: 'đ', value: 'd' },
    { key: 'Ạ', value: 'A' },
    { key: 'Ả', value: 'A' },
    { key: 'Ã', value: 'A' },
    { key: 'À', value: 'A' },
    { key: 'Á', value: 'A' },
    { key: 'Â', value: 'A' },
    { key: 'Ậ', value: 'A' },
    { key: 'Ầ', value: 'A' },
    { key: 'Ấ', value: 'A' },
    { key: 'Ẩ', value: 'A' },
    { key: 'Ẫ', value: 'A' },
    { key: 'Ă', value: 'A' },
    { key: 'Ắ', value: 'A' },
    { key: 'Ằ', value: 'A' },
    { key: 'Ặ', value: 'A' },
    { key: 'Ẳ', value: 'A' },
    { key: 'Ẵ', value: 'A' },
    { key: 'Ó', value: 'O' },
    { key: 'Ò', value: 'O' },
    { key: 'Ọ', value: 'O' },
    { key: 'Õ', value: 'O' },
    { key: 'Ỏ', value: 'O' },
    { key: 'Ô', value: 'O' },
    { key: 'Ộ', value: 'O' },
    { key: 'Ổ', value: 'O' },
    { key: 'Ỗ', value: 'O' },
    { key: 'Ồ', value: 'O' },
    { key: 'Ố', value: 'O' },
    { key: 'Ơ', value: 'O' },
    { key: 'Ờ', value: 'O' },
    { key: 'Ớ', value: 'O' },
    { key: 'Ợ', value: 'O' },
    { key: 'Ở', value: 'O' },
    { key: 'Ỡ', value: 'O' },
    { key: 'É', value: 'E' },
    { key: 'È', value: 'E' },
    { key: 'Ẻ', value: 'E' },
    { key: 'Ẹ', value: 'E' },
    { key: 'Ẽ', value: 'E' },
    { key: 'Ê', value: 'E' },
    { key: 'Ế', value: 'E' },
    { key: 'Ề', value: 'E' },
    { key: 'Ệ', value: 'E' },
    { key: 'Ể', value: 'E' },
    { key: 'Ễ', value: 'E' },
    { key: 'Ú', value: 'U' },
    { key: 'Ù', value: 'U' },
    { key: 'Ụ', value: 'U' },
    { key: 'Ủ', value: 'U' },
    { key: 'Ũ', value: 'U' },
    { key: 'Ư', value: 'U' },
    { key: 'Ự', value: 'U' },
    { key: 'Ữ', value: 'U' },
    { key: 'Ử', value: 'U' },
    { key: 'Ừ', value: 'U' },
    { key: 'Ứ', value: 'U' },
    { key: 'Í', value: 'I' },
    { key: 'Ì', value: 'I' },
    { key: 'Ị', value: 'I' },
    { key: 'Ỉ', value: 'I' },
    { key: 'Ĩ', value: 'I' },
    { key: 'Ý', value: 'Y' },
    { key: 'Ỳ', value: 'Y' },
    { key: 'Ỷ', value: 'Y' },
    { key: 'Ỵ', value: 'Y' },
    { key: 'Ỹ', value: 'Y' },
    { key: 'Đ', value: 'D' },
  ]

  let output = ''

  ;(input || 'Nguyễn Hoàng Văn Nhứt, LÊN NÚI ĐỂ KIẾM TIỀN').split('').forEach((char) => {
    const newChar = dic.find((item) => item.key === char)?.value
    output += newChar || char
  })

  return output
}

export const bytesToSize = (bytes) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Byte'
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1000)))
  return Math.round(bytes / Math.pow(1000, i), 2) + ' ' + sizes[i]
}

export const getFileExtension = (filename) => {
  // Tách chuỗi theo dấu chấm và lấy phần tử cuối cùng
  const extension = filename?.split('.').pop()
  return extension
}

export const getIconByFileType = (ext) => {
  ext = ext.toLowerCase()
  switch (ext) {
    case 'pdf':
      return faFilePdf
    case 'xls':
    case 'xlsx':
      return faFileExcel
    case 'doc':
    case 'docx':
    case 'docm':
      return faFileWord
    case 'pptm':
    case 'pptx':
      return faFilePowerpoint
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'heic':
      return faImage
    default:
      return faFile // default icon for unknown file types
  }
}

export const removeGuidFromFileName = (fileName) => {
  return fileName.split('_').slice(1).join('_') || fileName
}

// Utility function to capitalize the first letter of each key
export const capitalizeKeys = (obj) => {
  const newObj = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const newKey = key.charAt(0).toUpperCase() + key.slice(1)
      newObj[newKey] = obj[key]
    }
  }
  return newObj
}

export function generateDynamicTableColumns(rawTable = [], systemSettingTableColumns = []) {
  // Check if systemSettingTableColumns exists and is an array with elements
  if (Array.isArray(systemSettingTableColumns) && systemSettingTableColumns.length > 0) {
    // Generate tableColumns based on systemSettingTableColumns
    const tableColumns = systemSettingTableColumns.map((setting) => ({
      title: setting.col_name_display,
      dataIndex: setting.table_col_name || setting.xml_col_name, // Use the column name as the dataIndex
      key: setting.table_col_name || setting.xml_col_name, // Use the column name as the unique column key
      dataType: setting.datetime_type ? 'datetime' : setting.numeric_type ? 'number' : 'string', // Default to 'string' if no specific type is provided
      editable: setting?.col_editable,
      ...setting,
    }))

    return tableColumns
  }

  // Check if rawTable exists and is an array with elements
  if (Array.isArray(rawTable) && rawTable.length > 0) {
    // Extract unique keys from the first object in the array
    const keys = Object.keys(rawTable[0])

    // Generate tableColumns based on rawTable
    const tableColumns = keys.map((key) => ({
      title: key, // Format title
      dataIndex: key, // Use the key as the dataIndex
      key: key, // Use the key as the unique column key
      editable: true,
      width: '40%',
      dataType: typeof rawTable[0][key], // Get the data type of the value for this key
    }))

    return tableColumns
  }

  // Return an empty array if both systemSettingTableColumns and rawTable are empty
  return []
}

export const generateDynamicTableDataSoruce = (dataSource) => {
  if (!Array.isArray(dataSource) || dataSource.length === 0) {
    return []
  }
  return dataSource.map((item, index) => {
    return { key: index + 1, ...item }
  })
}

/**
 * Function to determine the data type based on metadata property and column record
 * @param {Object} systemSettingTableColumn - The column record
 * @param {Object} metadataProperty - The metadata property record
 * @returns {string} - The determined data type
 */
export function determineDataType(systemSettingTableColumn, metadataProperty) {
  if (systemSettingTableColumn) {
    if (systemSettingTableColumn.datetime_type) {
      return 'datetime'
    }
    if (systemSettingTableColumn.numeric_type) {
      return 'number'
    }
  }

  if (metadataProperty) {
    switch (metadataProperty['@_Type']) {
      case 'Edm.Int32':
      case 'Edm.Decimal':
        return 'number'
      case 'Edm.DateTimeOffset':
        return 'datetime'
      case 'Edm.Boolean':
        return 'boolean'
      case 'Edm.Guid':
        return 'guid'
      default:
        break
    }
  }

  return 'string' // Default data type
}

export function displayCurrency(value, popover = false) {
  if (value == 'N/A') {
    return '' // Return an empty instead of '0'
  }

  if (!value || value === null || value === undefined || value == 'N/A' || value <= 0) {
    return '0'
  }

  const formated = value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })

  // Keep display format without decimals
  return popover ? <Popover content={value}>{formated}</Popover> : formated
}

// Add new helper function for calculation rounding
export function roundCurrency(value, precision = 2) {
  if (!value || isNaN(value)) return 0
  return Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)
}

/**
 * Formats a date string into a specified format.
 *
 * @param {string} dateString - The date string to format.
 * @param {string} [format='DD/MM/YYYY'] - The format to apply to the date string.
 * @returns {Object|null} The formatted date object if valid, otherwise null.
 *
 * @example
 * // Returns a valid date object: "2025-02-26T17:00:00.000Z"
 * formatDateString('27/02/2025', 'DD/MM/YYYY');
 */
export const formatDateString = (dateString, format = 'DD/MM/YYYY') => {
  const formattedDate = dayjs(dateString, format)
  return formattedDate.isValid() ? formattedDate : null
}

// ...existing code...

/**
 * Converts a given date to a dayjs object if the date is valid, otherwise returns null.
 *
 * @param {string|Date} date - The date to be converted.
 * @returns {Object|null} A dayjs object representing the date, or null if the date is invalid.
 *
 * @example
 * const date = handleSetDateValue("2025-02-26T17:00:00.000Z");
 * console.log(date); // Outputs a dayjs object representing the date "2025-02-26T17:00:00.000Z"
 */
export const handleSetDateValue = (date) => (date ? dayjs(date) : null)

export const convertUTCToLocalTime = (date) => {
  if (!date) {
    return null
  }

  const localDate = dayjs(date)

  try {
    return localDate.format()
  } catch (error) {
    return null
  }
} // this function will returns local time like:  2025-02-18T00:00:00+07:00

export function getUniqueByKey(array, key) {
  return [...new Map(array.map((item) => [item[key], item])).values()]
}

export const handleResize =
  (index, columns, setColumns) =>
  (e, { size }) => {
    e.preventDefault()

    const newColumns = [...columns]
    newColumns[index] = {
      ...newColumns[index],
      width: size.width,
    }
    setColumns(newColumns)
  }

export const getContentLength = (data, dataIndex) => {
  if (!data || !Array.isArray(data) || data.length === 0) return 0

  const rawData = data[0]?.children
    ? data.reduce((acc, curr) => [...acc, ...curr.children], [])
    : data

  // Get max length of content in this column
  const maxLength = Math.max(
    ...rawData.map((row) => {
      const value = row[dataIndex]
      return value ? String(value).length * 8 : 0 // Approximate 8px per character
    }),
  )

  return Math.min(Math.max(maxLength + 32, 50), 300) // Min 50px, Max 300px, +32px for padding
}

export const makeResizableColumns = (cols, setCols, data = [], flexibleWidth = true) => {
  return [
    ...cols.map((col, index) => ({
      ...col,
      onHeaderCell: (column) => ({
        width: column.width,
        onResize: handleResize(index, cols, setCols),
        style: { whiteSpace: 'normal', overflow: 'visible', fontSize: '0.8rem' },
      }),
      ellipsis: true,
      onCell: () => ({
        ...col.onCell,
        style: {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          ...col.onCell?.style,
        },
      }),
      sorter: (a, b) => {
        if (a[col.dataIndex] < b[col.dataIndex]) {
          return -1
        }
        if (a[col.dataIndex] > b[col.dataIndex]) {
          return 1
        }
        return 0
      },
      filterSearch: true,
      filters: getColumnFilterList(data, col.dataIndex),
      onFilter: (value, record) => {
        if (record.children) {
          return true
        }

        const fieldValue = record[col.dataIndex]
        if (fieldValue == null) return false
        return fieldValue.toString().toLowerCase().includes(value.toLowerCase())
      },
      filterIcon: (filtered) => (
        <div className="ps-1">
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        </div>
      ),
      width: col.width + 50, // for sort icon
    })),
  ]
}

export const getColumnFilterList = (data, dataIndex) => {
  if (!data || !Array.isArray(data)) return []
  const rawData = data[0]?.children
    ? data.reduce((acc, curr) => [...acc, ...curr.children], [])
    : data

  // Get unique values for the column
  const uniqueValues = [
    ...new Set(rawData.map((item) => item[dataIndex]).filter((value) => value != null)),
  ]

  // Convert to filter format required by Ant Design
  return uniqueValues.map((value) => ({
    text: isNumber(value) ? displayCurrency(value) : value.toString(),
    value: value.toString(),
  }))
}

export const delay = (time) => new Promise((resolve) => setTimeout(resolve, time))

/**
 * Calculates the total sum of values for a specific column in an array of data rows.
 *
 * @param {Array<Object>} dataRows - An array of objects representing the data rows.
 * @param {string} columnName - The name of the column whose values should be summed.
 * @returns {number} The total sum of the values in the specified column. If a value is undefined or not a number, it is treated as 0.
 */
export const calculateTotalForColumn = (dataRows, columnName) => {
  return dataRows.reduce((total, row) => total + (Number(row[columnName]) || 0), 0)
}

export async function batchGetItemsByColumnName(data, columnName, listName, batchSize = 20) {
  // Split data into batches
  const getUserBatch = (usernameBatch) => async () => {
    const filter = usernameBatch.map((username) => `${columnName} eq '${username}'`).join(' or ')
    const data = await getItemsService(listName, { filter, top: usernameBatch.length })
    return data.value || []
  }

  // Create batches
  const usernameBatches = []
  for (let i = 0; i < data.length; i += batchSize) {
    usernameBatches.push(data.slice(i, i + batchSize))
  }

  // Process batches
  const batchResults = await batchPromises(
    usernameBatches.map((batch) => () => getUserBatch(batch)()),
    batchSize,
  )

  // Combine and return results
  return batchResults.flat()
}

function removeVietnameseTones(str) {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D')
}

export const makeFilterColumns = (cols, dataSource) => {
  return cols.map((col) => ({
    ...col,
    filterSearch: true,
    filters: getColumnFilterList(dataSource, col.dataIndex),
    onFilter: (value, record) => {
      if (record.children) {
        return true
      }

      const fieldValue = record[col.dataIndex]
      if (fieldValue == null) return false
      return fieldValue.toString().toLowerCase().includes(value.toLowerCase())
    },
    filterIcon: (filtered) => (
      <div className="ps-1">
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      </div>
    ),
  }))
}

export const logDebug = (name, data) => {
  console.log(name || 'logDebug' + ': ', data)
}

export function convertKeysFirstCharLower(obj) {
  const result = {}
  for (const key in obj) {
    const newKey = key.charAt(0).toLowerCase() + key.slice(1)
    result[newKey] = obj[key]
  }
  return result
}

export const formatHN = (searchHN) => {
  return searchHN.length === 9 ? searchHN : '8' + searchHN?.padStart(8, '0')
}

export const isNotToday = (date) => {
  if (!date) {
    return true
  }
  return dayjs(date).isBefore(dayjs().startOf('day'))
}
